using System;
using System.Linq;
using System.Web.Mvc;
using Inus_AppsMang.Models;

namespace Inus_AppsMang.Controllers
{
    public class d074Controller : Controller
    {
        private Insh_AppsDBEntities db = new Insh_AppsDBEntities();
        private Insur_class ic_cl = new Insur_class();

        public ActionResult d074000()
        {
            return View("MainAccountPage");
        }

        [HttpPost]
        public JsonResult d074001(Guid UserID)
        {
            try
            {
                if (!ic_cl.HasPer(UserID.ToString(), 123))
                    return Json(new { ErrorCode = 3 });

                var obj = db.AgenMainAcc.Where(x => x.Status != 3).ToList();
                var result = obj.Select(x => new
                {
                    ID = x.AccountID,
                    Name = x.AccDesc,
                    x.Status,
                    InsertDate = x.InsertDate.Value.ToString("yyyy-MM-dd hh:mm tt"),
                    InsertedBy = db.Users.FirstOrDefault(u => u.UserID == x.InsertBy).UserName
                }).ToList();

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpPost]
        public JsonResult d074002(AgenMainAcc model, Guid UserID)
        {
            try
            {
                if (!ic_cl.HasPer(UserID.ToString(), 123))
                    return Json(new { ErrorCode = 3 });
                model.InsertDate = DateTime.Now;
                model.InsertBy = UserID;
                model.Status = 1;
                db.AgenMainAcc.Add(model);
                db.SaveChanges();

                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpPost]
        public JsonResult d074003(AgenMainAcc model, Guid UserID)
        {
            try
            {
                if (!ic_cl.HasPer(UserID.ToString(), 123))
                    return Json(new { ErrorCode = 3 });

                var account = db.AgenMainAcc.FirstOrDefault(x => x.AccountID == model.AccountID);

                if (account != null)
                {
                    account.AccDesc = model.AccDesc;
                    account.UpdateDate = DateTime.Now;
                    account.UpdateBy = UserID;
                    db.SaveChanges();
                    return Json(new { success = true });
                }

                return Json(new { error = "التصنيف الرئيسي غير موجود" });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpPost]
        public JsonResult d074004(int id, byte status, Guid UserID)
        {
            try
            {
                if (!ic_cl.HasPer(UserID.ToString(), 123))
                    return Json(new { ErrorCode = 3 });

                var account = db.AgenMainAcc.FirstOrDefault(x => x.AccountID == id);

                if (account != null)
                {
                    account.Status = status;
                    account.UpdateDate = DateTime.Now;
                    account.UpdateBy = UserID;

                    db.SaveChanges();
                    return Json(new { success = true });
                }

                return Json(new { error = "التصنيف الرئيسي غير موجود" });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
