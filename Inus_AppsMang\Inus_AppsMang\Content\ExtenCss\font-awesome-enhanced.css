/* Font Awesome 6 fixes */
@font-face {
    font-family: 'Font Awesome 6 Free';
    font-style: normal;
    font-weight: 900;
    font-display: block;
    src: url("https://use.fontawesome.com/releases/v6.5.1/webfonts/fa-solid-900.woff2") format("woff2");
}

@font-face {
    font-family: 'Font Awesome 6 Free';
    font-style: normal;
    font-weight: 400;
    font-display: block;
    src: url("https://use.fontawesome.com/releases/v6.5.1/webfonts/fa-regular-400.woff2") format("woff2");
}

@font-face {
    font-family: 'Font Awesome 6 Brands';
    font-style: normal;
    font-weight: 400;
    font-display: block;
    src: url("https://use.fontawesome.com/releases/v6.5.1/webfonts/fa-brands-400.woff2") format("woff2");
}

/* Ensure icons display properly */
.fa,
.fas,
.far,
.fal,
.fab {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

.fa-solid, .fas {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

.fa-regular, .far {
    font-family: 'Font Awesome 6 Free';
    font-weight: 400;
}

.fa-brands, .fab {
    font-family: 'Font Awesome 6 Brands';
    font-weight: 400;
}

/* Enhanced Icon Styles */
.icon-hover {
    transition: all 0.3s ease;
}

.icon-hover:hover {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.icon-spin {
    animation: fa-spin 2s infinite linear;
}

.icon-pulse {
    animation: fa-pulse 1s infinite;
}

.icon-shake:hover {
    animation: fa-shake 0.5s ease-in-out;
}

.icon-bounce:hover {
    animation: fa-bounce 0.6s ease-in-out;
}

/* Icon Animations */
@keyframes fa-shake {
    0%, 100% { transform: rotate(0deg); }
    20%, 60% { transform: rotate(6deg); }
    40%, 80% { transform: rotate(-6deg); }
}

@keyframes fa-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-6px); }
}

/* Icon Size Classes */
.icon-xs { font-size: 0.75rem; }
.icon-sm { font-size: 0.875rem; }
.icon-md { font-size: 1rem; }
.icon-lg { font-size: 1.25rem; }
.icon-xl { font-size: 1.5rem; }
.icon-2xl { font-size: 2rem; }
.icon-3xl { font-size: 3rem; }

/* Icon Colors with Hover Effects */
.icon-primary { color: var(--bs-primary); }
.icon-primary:hover { color: var(--bs-primary-dark, #0056b3); }

.icon-success { color: var(--bs-success); }
.icon-success:hover { color: var(--bs-success-dark, #1e7e34); }

.icon-warning { color: var(--bs-warning); }
.icon-warning:hover { color: var(--bs-warning-dark, #d39e00); }

.icon-danger { color: var(--bs-danger); }
.icon-danger:hover { color: var(--bs-danger-dark, #bd2130); }

.icon-info { color: var(--bs-info); }
.icon-info:hover { color: var(--bs-info-dark, #117a8b); }

/* Button Icon Spacing */
.btn i {
    margin-left: 0.5rem;
    vertical-align: middle;
}

/* Icon Container Styles */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    transition: all 0.3s ease;
}

.icon-circle:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.2);
    transform: scale(1.05);
}

/* Ensure Bootstrap Icons display properly */
.bi {
    display: inline-block;
    vertical-align: -.125em;
    fill: currentColor;
} 