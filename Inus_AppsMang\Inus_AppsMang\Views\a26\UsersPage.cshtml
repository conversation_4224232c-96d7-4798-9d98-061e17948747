﻿<link href="/Content/dist/css/bootstrap.rtl.min.css" rel="stylesheet" />
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="row justify-content-center m-2 ">

    <div class="col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-if="ctrl.switchFlag==1">
        <div class="card">
            <div class="card-header cairo  " dir="rtl">
                <div class="btn-group mx-2  " dir="ltr" role="group" aria-label="Basic example">
                    <button type="button" disabled class="btn btn-dark  ">
                        <i class="fa fa-users" aria-hidden="true"></i>
                        إدارة المستخدمين
                    </button>
                    @*<button type="button" class="btn btn-primary bi bi-house-door" ng-click="ctrl.BtnHomePage()"> الصفحة الرئيسية</button>*@
                    <button type="button" class="btn btn-primary bi bi-house-door" ng-click="ctrl.sys_page()">  أنظمة الإدارة</button>
                </div>
                <div class="col-lg-5 col-md-6 col-sm-12 col-xs-12 col-xl-5 col-xxl-5 float-end">

                    <button type="button"
                            class="btn btn-success cairo  float-end  "
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.AddNew()">
                        إضافة مستخدم
                        <i class="fa fa-user-plus" aria-hidden="true"></i>
                    </button>

                </div>
            </div>

            <div class="card-body">

                <div class="row justify-content-center">
                    <div class="col-lg-7 col-md-12 col-sm-12 col-xs-12 col-xl-7 col-xxl-7  ">

                        <input class="form-control inputStyle1 cairo" type="text" ng-model="serchUser" placeholder="بحث بإسم المستخدم...">


                    </div>
                   
                </div>
                <div class="row mt-4">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover cairo">
                            <tr class="l-bg-blue-dark text-white">
                                <th class="text-center">#</th>
                                <th class="text-center">اسم المستخدم</th>
                                <th class="text-center">الإسم الثلاثي</th>
                                <th class="text-center">تاريخ الإنشاء</th>
                                <th class="text-center">أضيف من قبل</th>
                                <th class="text-center">الحالة</th>
                                <th class="text-center" colspan="4">العمليات</th>
                            </tr>
                            <tr ng-repeat="x in ctrl.Users | filter:serchUser">
                                <td class="text-center">{{$index + 1}}</td>
                                <td class="text-center">{{x.UsName}}</td>
                                <td class="text-center">{{x.FullName}}</td>
                                <td class="text-center">{{x.InsertDate}}</td>
                                <td class="text-center">{{x.InsertedBy}}</td>
                                <td class="text-center">{{x.Status == 1 ?   "مفعل" : "موقوف"}}</td>
                                <td>
                                    <button type="button" class="btn btn-outline-primary col-12" ng-click="ctrl.a260059(x)"> الصلاحيات</button>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-dark col-12" ng-click="ctrl.FnPerms(x)">({{x.PerCount}}) الإشراف</button>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-primary" ng-click="ctrl.BtnMainEdit(x)">
                                        تعديل
                                        <i class="fa fa-edit"></i>

                                    </button>
                                </td>
                                <td ng-show="x.Status == 1">
                                    <button type="button" class="btn btn-warning" ng-click="ctrl.BtnStatus(x,2)">
                                        إيقاف
                                        <i class="fa fa-toggle-off"></i>

                                    </button>
                                </td>
                                <td ng-show="x.Status == 2">
                                    <button type="button" class="btn btn-success" ng-click="ctrl.BtnStatus(x,1)">
                                        تفعيل
                                        <i class="fa fa-toggle-on"></i>

                                    </button>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x,3)">
                                        حذف
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>

                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>







    <div class="col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-if="ctrl.switchFlag==2">
        <div class="card">
            <div class="card-header cairo  " dir="rtl">
                <div class="btn-group mx-2  " dir="ltr" role="group" aria-label="Basic example">
                    <button type="button" disabled class="btn btn-dark  ">
                        <i class="fa fa-users" aria-hidden="true"></i>
                         الإشراف
                    </button>
                    <button type="button" class="btn btn-success  " ng-click="ctrl.btnBack()">
                        <i class="fa fa-users" aria-hidden="true"></i>
                        إدارة المستخدمين
                    </button>
                    <button type="button" class="btn btn-primary bi bi-house-door" ng-click="ctrl.BtnHomePage()"> الصفحة الرئيسية</button>
                </div>
            </div>

            <div class="card-body">

                <div class="row">
                    <div class="col-lg-3 col-md-12 col-sm-12 col-xs-12 col-xl-3 col-xxl-3">
                        <p>
                            اسم المستخدم :  {{ctrl.fullname}}
                        </p>


                    </div>
                    <div class="col-lg-7 col-md-12 col-sm-12 col-xs-12 col-xl-7 col-xxl-7">
                        <input class="form-control inputStyle2  cairo" type="text" ng-model="serchOffice" placeholder="بحث بإسم المكتب...">


                    </div>
                    <div class="col-lg-2 col-md-12 col-sm-12 col-xs-12 col-xl-2 col-xxl-2">

                        <button type="button"
                                class="btn btn-primary cairo     "
                                  ng-click="ctrl.AddNew1()">
                            إضافة مكتب إشراف
                            <i class="fa fa-user-plus" aria-hidden="true"></i>
                        </button>

                    </div>
                </div>
                <div class="row mt-3">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover cairo">
                            <tr class="l-bg-green-dark text-white">
                                <th class="text-center">#</th>
                                <th class="text-center">اسم المكتب</th>
                                <th class="text-center">  تاريخ الاضافة</th>
                                <th class="text-center">    اضيفت بواسطة</th>
                                <th class="text-center" colspan="2">العمليات</th>
                            </tr>
                            <tr ng-repeat="x in ctrl.ResAgenciesTb | filter :serchOffice">
                                <td class="text-center">{{$index + 1}}</td>
                                <td class="text-center">{{x.AgenName}}</td>
                                <td class="text-center">{{x.InsertDate}}</td>
                                <td class="text-center">{{x.InsertBy}}</td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus1(x,true)">
                                        حذف
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>

                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-if="ctrl.switchFlag==3">

    <div class="btn-group mx-2  " dir="ltr" role="group" aria-label="Basic example">
        <button type="button" disabled class="btn btn-dark  ">
            <i class="fa fa-users" aria-hidden="true"></i>
            صلاحيات المستخدمين
        </button>
        <button type="button" class="btn btn-success  " ng-click="ctrl.btnBack()">
            <i class="fa fa-users" aria-hidden="true"></i>
            إدارة المستخدمين
        </button>
        <button type="button" class="btn btn-primary bi bi-house-door" ng-click="ctrl.BtnHomePage()"> الصفحة الرئيسية</button>
    </div>

    <h1></h1>
    <h3 class="text-center" style="font-family:'Times New Roman'">صلاحيات المستخدم:  {{ctrl.selectedusername}}</h3>

    <div class="card border-1 border-dark col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center justify-content-center" style="font-family: 'Times New Roman'; color:black !important; height: 50px !important; width:900px !important ">
                صلاحيات الأنظمة الرئيسية
            </h4>
            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.InsuranceInterfacemanagmentSys" id="ch_InsuranceInterfacemanagmentSys" name="InsuranceInterfacemanagmentSys" autocomplete="off">
                    <label class="btn btn-outline-dark" for="ch_InsuranceInterfacemanagmentSys">نظام إدارة واجهات التأمين</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.agency_sys" id="ch_agency_sys" name="agency_sys" autocomplete="off">
                    <label class="btn btn-outline-dark" for="ch_agency_sys">نظام المتابعة</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.finance_sys" id="ch_finance_sys" name="finance_sys" autocomplete="off">
                    <label class="btn btn-outline-dark" for="ch_finance_sys">نظام المالية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.agincu_contractEnd" id="ch_agincu_contractEnd" name="agincu_contractEnd" autocomplete="off">
                    <label class="btn btn-outline-dark" for="ch_agincu_contractEnd">إنهاء التعاقد مع الوكلاء</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.inventory_a_requests" id="ch_inventory_a_requests" name="inventory_a_requests" autocomplete="off">
                    <label class="btn btn-outline-dark" for="ch_inventory_a_requests">إنهاء التعاقد مع الوكلاء</label>
                </div>
               

            </div>
        </div>
    </div>
    <div class="card border-1 border-secondary col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary pt-4 text-center" style="font-family: 'Times New Roman'; color: gray !important; width: 900px !important">
                الصلاحيات العامة
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@

            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.users" id="ch_users" name="users" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_users"> المستخدمين</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.agency" id="ch_agency" name="agency" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_agency">الوكالات</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.comp_contries" id="ch_comp_contries" name="comp_contries" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_comp_contries">البلدان</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.settings" id="ch_settings" name="settings" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_settings">الإعدادات</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.news" id="ch_news" name="news" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_news">الأخبار</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.palance" id="ch_palance" name="palance" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_palance">الميزانية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.reports" id="ch_reports" name="reports" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_reports">التقارير</label>
                </div>

                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.InsuranceInterfacemanagmentSys" ng-model="ctrl.PerObj.compolsy_Sys" id="ch_compolsy_Sys" name="compolsy_Sys" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_compolsy_Sys">نظام الإجباري</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.InsuranceInterfacemanagmentSys" ng-model="ctrl.PerObj.traviles_Sys" id="ch_traviles_Sys" name="traviles_Sys" autocomplete="off">
                    <label class="btn btn-outline-success" for="ch_traviles_Sys">نظام المسافرين</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.InsuranceInterfacemanagmentSys" ng-model="ctrl.PerObj.orang_sys" id="ch_orang_sys" name="orang_sys" autocomplete="off">
                    <label class="btn btn-outline-warning" for="ch_orang_sys">نظام البرتقالية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.InsuranceInterfacemanagmentSys" ng-model="ctrl.PerObj.medical_sys" id="ch_medical_sys" name="medical_sys" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_medical_sys">نظام المسؤولية الطبية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.PerObj.MainAcc" id="ch_MainAcc" name="MainAcc" autocomplete="off">
                    <label class="btn btn-outline-info" for="ch_MainAcc">MainAcc</label>
                </div>
            </div>
        </div>
    </div>

    <div class="card border-1 border-primary col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.PerObj.compolsy_Sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-primary text-center" style="font-family: 'Times New Roman'; color: blue !important; width: 900px !important">
                صلاحيات نظام الإجباري
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@

            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_Mangments" id="ch_comp_Mangments" name="comp_Mangments" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_Mangments">الإدارة</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_Color" id="ch_comp_Color" name="comp_Color" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_Color">الألوان</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_seralies" id="ch_comp_seralies" name="comp_seralies" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_seralies">المخزون</label>
                </div>

                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_deleteAprovels" id="ch_comp_deleteAprovels" name="comp_deleteAprovels" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_deleteAprovels">الموافقة على طلبات الحذف</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_inshDurations" id="ch_comp_inshDurations" name="comp_inshDurations" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_inshDurations">مدة التأمين</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_carTypes" id="ch_comp_carTypes" name="comp_carTypes" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_carTypes">أنواع السيارات</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_compaines" id="ch_comp_compaines" name="comp_compaines" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_compaines">الشركات</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.compolsy_Sys" ng-model="ctrl.PerObj.comp_reports" id="ch_comp_reports" name="comp_reports" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_comp_reports">التقارير</label>
                </div>
            </div>
        </div>
    </div>

    <div class="card border-1 border-success col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.PerObj.traviles_Sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center" style="font-family: 'Times New Roman'; color: green !important; width: 900px !important">
                صلاحيات نظام المسافرين
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@

            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.traviles_Sys" ng-model="ctrl.PerObj.trav_contries" id="ch_trav_contries" name="trav_contries" autocomplete="off">
                    <label class="btn btn-outline-success" for="ch_trav_contries">البلدان</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.traviles_Sys" ng-model="ctrl.PerObj.trav_ranges" id="ch_trav_ranges" name="trav_ranges" autocomplete="off">
                    <label class="btn btn-outline-success" for="ch_trav_ranges">نطاق المسافرين</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.traviles_Sys" ng-model="ctrl.PerObj.trav_Seariles" id="ch_trav_Seariles" name="trav_Seariles" autocomplete="off">
                    <label class="btn btn-outline-success" for="ch_trav_Seariles">المخزون</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.traviles_Sys" ng-model="ctrl.PerObj.trav_reports" id="ch_trav_reports" name="trav_reports" autocomplete="off">
                    <label class="btn btn-outline-success" for="ch_trav_reports">التقارير</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.traviles_Sys" ng-model="ctrl.PerObj.trav_deletedPapers" id="ch_trav_deletedPapers" name="trav_deletedPapers" autocomplete="off">
                    <label class="btn btn-outline-success" for="ch_trav_deletedPapers">الوثائق الملغية</label>
                </div>
            </div>
        </div>
    </div>

    <div class="card border-1 border-warning col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.PerObj.orang_sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center justify-content-center" style="font-family: 'Times New Roman'; color: orange !important; height: 50px !important; width:900px !important ">
                صلاحيات نظام البرتقالية
            </h4>
            <div class="row card-body pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.orang_sys" ng-model="ctrl.PerObj.Orange_Contries" id="ch_Orange_Contries" name="Orange_Contries" autocomplete="off">
                    <label class="btn btn-outline-warning" for="ch_Orange_Contries">البلدان</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.orang_sys" ng-model="ctrl.PerObj.Orang_cars" id="ch_Orang_cars" name="Orang_cars" autocomplete="off">
                    <label class="btn btn-outline-warning" for="ch_Orang_cars">السيارات</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.orang_sys" ng-model="ctrl.PerObj.Orang_Seariles" id="ch_Orange_Inventory" name="Orange_Inventory" autocomplete="off">
                    <label class="btn btn-outline-warning" for="ch_Orange_Inventory">المخزون</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.orang_sys" ng-model="ctrl.PerObj.orang_reports" id="ch_orang_reports" name="orang_reports" autocomplete="off">
                    <label class="btn btn-outline-warning" for="ch_orang_reports">التقارير</label>
                </div>
            </div>
        </div>
    </div>

    <div class="card border-1 border-danger col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.PerObj.medical_sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center" style="font-family: 'Times New Roman'; color: red !important; width: 900px !important">
                صلاحيات نظام المسؤولية الطبية
            </h4>
            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.medical_sys" ng-model="ctrl.PerObj.med_adress" id="ch_med_adress" name="med_adress" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_med_adress">العنوان</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.medical_sys" ng-model="ctrl.PerObj.med_job" id="ch_med_job" name="med_job" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_med_job">المهنة </label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.medical_sys" ng-model="ctrl.PerObj.med_nat" id="ch_med_nat" name="med_nat" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_med_nat">الجنسية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.medical_sys" ng-model="ctrl.PerObj.marital_stat" id="ch_marital_stat" name="marital_stat" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_marital_stat">الحالة الإجتماعية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.medical_sys" ng-model="ctrl.PerObj.med_percentage" id="ch_med_percentage" name="med_percentage" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_med_percentage">إدارة النسب</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.medical_sys" ng-model="ctrl.PerObj.med_academic" id="ch_med_academic" name="med_academic" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_med_academic">المؤهل العلمي</label>
                </div>
            </div>
        </div>
    </div>
    <div class="card border-1 border-dark col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.PerObj.agency_sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center justify-content-center" style="font-family: 'Times New Roman'; color:black !important; height: 50px !important; width:900px !important ">
                صلاحيات نظام المتابعة
            </h4>
            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.PerObj.agency_sys" ng-model="ctrl.PerObj.agency_deleteAprovels" id="ch_agency_deleteAprovels" name="agency_deleteAprovels" autocomplete="off">
                    <label class="btn btn-outline-dark" for="ch_agency_deleteAprovels">تأكيد الحذف</label>
                </div>
            </div>
        </div>
    </div>
    <div class="card border-1 border-white col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.PerObj.finance_sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center justify-content-center" style="font-family: 'Times New Roman'; color:black !important; height: 50px !important; width:900px !important ">
                صلاحيات نظام المالية
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@
        </div>
    </div>
    
    <!-- صلاحيات طلبات المخزون -->
    <div class="card border-1 border-info col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:160px !important">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center justify-content-center" style="font-family: 'Times New Roman'; color: #17a2b8 !important; height: 50px !important; width:900px !important ">
                صلاحيات إدارة طلبات المخزون
            </h4>
            <div class="row pt-2 justify-content-center">
               
            </div>
        </div>
    </div>
    <div class="modal-footer justify-content-center pt-2">
        <button type="button" class="btn cairo btn-success col-lg-3 col-md-3 col-sm-3 col-xl-3 col-xxl-3 justify-content-center" ng-click="ctrl.a260071()">
            حفظ
        </button>
    </div>

</div>

        <div class="modal fade cairo" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
            <div class="modal-dialog ">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body cairo">
                        <form name="mFrm" class="row g-3" autocomplete="off" novalidate>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12 ">
                                <div class="col-auto m-4 " ng-hide="ctrl.DlFalg == 1">
                                    <label for="PhoneNum">نوع المستخدم</label>
                                    <select class="form-select" name="UsType" required ng-model="ctrl.MainObj.UsType">
                                        <option value="1">مستخدم إدارة وافد</option>
                                        <option value="2">مطلع تقارير(مستخدمي الوزارة)</option>
                                        <option value="3">مطلع مؤشرات أداء</option>
                                    </select>
                                    <span ng-show="mFrm.PhoneNum.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                                    <span ng-show="mFrm.PhoneNum.$error.pattern" class="text-danger float-end m-2">أكتب رقم الهاتف بشكل صحيح</span>
                                    <span ng-show="mFrm.PhoneNum.$valid" class="text-success bi-check-lg float-end m-2"></span>

                                </div>
                                <div class="col-auto m-4 cairo">
                                    <label for="UsName">اسم المستخدم</label>
                                    <input required pattern="^(?=.*[a-z])(?!.*\s).*$" maxlength="20" minlength="5" name="UsName" type="text"
                                           @*pattern="[A-Za-z0-9_]{1,32}$"*@
                                           ng-model="ctrl.MainObj.UsName" ng-blur="ctrl.IsExisit()" class="form-control ">
                                    <span ng-show="mFrm.UsName.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                                    <span ng-show="mFrm.UsName.$error.minlength && mFrm.UsName.$error.pattern == null" class="text-danger float-end m-2">اقل عدد حروف  5</span>
                                    <span ng-show="mFrm.UsName.$error.maxlength && mFrm.UsName.$error.pattern == null" class="text-danger float-end m-2">أكثر عدد حروف 20</span>
                                    <span ng-show="mFrm.UsName.$error.pattern" class="text-danger float-end m-2">أكتب إسم المستخدم بشكل صحيح</span>
                                    <span ng-show="mFrm.UsName.$valid && ctrl.IsEx == false" class="text-success bi-check-lg float-end m-2"></span>
                                    <span ng-show="mFrm.UsName.$valid && ctrl.IsEx " class="text-danger float-end m-2">هذا الإسم موجود مسبقا</span>
                                </div>
                                <div class="col-auto m-4 cairo">
                                    <label for="FullName">الإسم الثلاثي</label>
                                    <input required maxlength="20" minlength="5" ng-model="ctrl.MainObj.FullName" type="text" name="FullName" class="form-control">
                                    <span ng-show="mFrm.FullName.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                                    <span ng-show="mFrm.FullName.$error.minlength" class="text-danger float-end m-2">اقل عدد حروف  5</span>
                                    <span ng-show="mFrm.FullName.$error.maxlength" class="text-danger float-end m-2">أكثر عدد حروف 20</span>
                                    <span ng-show="mFrm.FullName.$valid" class="text-success bi-check-lg float-end m-2"></span>
                                </div>
                                <div class="col-auto m-4 cairo">
                                    <label for="PhoneNum">رقم الهاتف</label>
                                    <input pattern="^(?:0|\(?\+33\)?\s?|0033\s?)[1-79](?:[\.\-\s]?\d\d){4}$" ng-model="ctrl.MainObj.PhoneNum"
                                           name="PhoneNum" class="form-control" required>
                                    <span ng-show="mFrm.PhoneNum.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>

                                    <span ng-show="mFrm.PhoneNum.$error.pattern" class="text-danger float-end m-2">أكتب رقم الهاتف بشكل صحيح</span>
                                    <span ng-show="mFrm.PhoneNum.$valid" class="text-success bi-check-lg float-end m-2"></span>

                                </div>
                                <div class="col-auto m-4 cairo">
                                    <label for="Password">كملة المرور</label>
                                    <input name="Password" pattern="^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.*\s).*$"
                                           required ng-model="ctrl.MainObj.Password" type="password" class="form-control">
                                    <span ng-show="mFrm.Password.$error.reqErrorCodeuired" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                                    <span ng-show="mFrm.Password.$error.minlength" class="text-danger float-end m-2">اقل عدد حروف  5</span>
                                    <span ng-show="mFrm.Password.$error.pattern" class="text-danger float-end m-2">يجب أن يحتوي على حروف كبيرة و حروف صغيرة وأرقام</span>
                                    <span ng-show="mFrm.Password.$valid" class="text-success bi-check-lg float-end m-2"></span>

                                </div>
                                <div class="col-auto m-4 cairo">
                                    <label for="RePassword">تأكيد كلمة المرور</label>
                                    <input ng-disabled="mFrm.Password.$error.pattern" name="RePassword" required type="password" class="form-control" ng-model="ctrl.RePassword">
                                    <span ng-show="mFrm.RePassword.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                                    <span ng-show="(ctrl.MainObj.Password != ctrl.RePassword) && mFrm.RePassword.$error.required == null " class="text-danger float-end m-2">كلمة المرور غير متطابقة</span>
                                    <span ng-show="(ctrl.MainObj.Password == ctrl.RePassword && mFrm.RePassword.$error.required  == null ) " class="text-success bi-check-lg float-end m-2"></span>

                                </div>
                            </div>



                        </form>
                    </div>
                    <div class="modal-footer" ng-show=" ctrl.DlFalg == 0">
                        <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.Save()"
                                @*ng-disabled="mFrm.$invalid || ctrl.MainObj.Password != ctrl.RePassword   && ctrl.IsEx == true"*@ data-bs-dismiss="modal">
                            حفظ
                        </button>
                        <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                    </div>
                    <div class="modal-footer" ng-show=" ctrl.DlFalg == 1">
                        <button type="button" class="btn btn-primary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a25005()"
                                ng-disabled="mFrm.$invalid || ctrl.MainObj.Password != ctrl.RePassword   && ctrl.IsEx == true" data-bs-dismiss="modal">
                            تعديل
                        </button>
                        <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="Dl_Message" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
            <div class="modal-dialog ">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-auto">
                                <h4 class="m-4 text-danger">{{ctrl.DlMessage}}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a25006()"
                                data-bs-dismiss="modal">
                            نعم
                        </button>
                        <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">لا</button>
                    </div>

                </div>
            </div>
        </div>
        <div class="modal fade" id="Dl_Message1" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
            <div class="modal-dialog ">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-auto">
                                <h4 class="m-4 text-danger">{{ctrl.DlMessage}}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a25008()"
                                data-bs-dismiss="modal">
                            نعم
                        </button>
                        <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">لا</button>
                    </div>

                </div>
            </div>
        </div>

        <div class="modal fade cairo" id="perDialog" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header cairo">
                        <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body cairo">
                        <form name="perFrm" class="row g-3" autocomplete="off" novalidate>

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12">

                                <div class="row  ">
                                    <label>خارج الإشراف</label>
                                    <div class="col-12">
                                        <input type="text" class="form-control" ng-model="Search.ResAgeLi" role="search" placeholder="بحث ... اسم الوكيل" />
                                    </div>
                                    <div class="col-12">
                                        <select size="10" class="form-select col-12 mt-1" multiple
                                                ng-options=" x.ID as x.Name  for x in ctrl.ResAgenLI |filter:Search.ResAgeLi" ng-model="ctrl.ResObj.AgencyID">
                                        </select>
                                    </div>


                                </div>
                            </div>

                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn cairo btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a260058()"
                                data-bs-dismiss="modal">
                            حفظ
                        </button>
                        <button type="button" class="btn cairo btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                    </div>

                </div>
            </div>
        </div>
