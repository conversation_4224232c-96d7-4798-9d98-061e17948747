﻿<link href="~/Content/StyleSheet1.css" rel="stylesheet" media="screen" />
<div class="row justify-content-center m-2 ">

    <div class="col-lg-12 bg-img col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12 softDark">
        <div class="card  navglassColor ">
            <div class="card-header">

                <div class=" cairo btn-group" dir="ltr" role="group" aria-label="Basic mixed styles example">
                    <button type="button" disabled class="btn btn-danger"> إدارة الحساب الرئيسي</button>
                    <button type="button" ng-click="ctrl.Home()" class="btn btn-outline-primary">
                        الشاشة الرئيسية
                        <i class="bi bi-house-fill"></i>
                    </button>
                </div>
                <div class="float-end">
                    <button type="button"
                            class="btn btn-success cairo"
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.AddNew()">
                        إضافة حساب رئيسي
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="">
            <div class="row m-0 justify-content-center">
                <div class="col-sm-12 col-xs-12  offset-1 col-md-5 col-lg-5 col-xl-5 col-xxl-5">
                    <input type="text" class=" cairo inputStyle" ng-model="searchAccount" placeholder="  بحث بالحساب الرئيسي...  ">
                </div>
            </div>

            <div class="row  mt-4 cairo" style="height:90vh; padding:15px">
                <div class="table-responsive">
                    <table class="table  table-striped table-hover">
                        <tr class="bg-dark text-white">
                            <th class="text-center">#</th>
                            <th class="text-center">اسم الحساب</th>
                            <th class="text-center">المدخل</th>
                            <th class="text-center">تاريخ الإدخال</th>
                            <th class="text-center">الحالة</th>
                            <th class="text-center" colspan="3">العمليات</th>
                        </tr>
                        <tr class="navglassColor text-white"
                            ng-repeat="x in ctrl.AccountList | filter:searchAccount">
                            <td class="text-center">{{$index + 1}}</td>
                            <td class="text-center">{{x.Name}}</td>
                            <td class="text-center">{{x.InsertedBy}}</td>
                            <td class="text-center">{{x.InsertDate}}</td>
                            <td class="text-center">{{x.Status == 1 ? "مفعل" : "موقوف"}}</td>

                            <td>
                                <button type="button" class="btn btn-primary" ng-click="ctrl.BtnMainEdit(x)">
                                    تعديل
                                    <i class="fa fa-edit"></i>
                                </button>
                            </td>
                            <td ng-show="x.Status == 1">
                                <button type="button" class="btn btn-warning" ng-click="ctrl.BtnStatus(x,2)">
                                    إيقاف
                                    <i class="fa fa-toggle-off"></i>
                                </button>
                            </td>
                            <td ng-show="x.Status == 2">
                                <button type="button" class="btn btn-success" ng-click="ctrl.BtnStatus(x,1)">
                                    تفعيل
                                    <i class="fa fa-toggle-on"></i>
                                </button>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x,3)">
                                    حذف
                                    <i class="fa fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="NewItem" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title col-11">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close col-1" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <form name="mForm" class="row g-3" autocomplete="off" novalidate>
                    <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 col-xl-12 col-xl-10 ">
                        <div class="col-auto m-6">
                            <label for="Name">اسم الحساب</label>
                            <input required name="Name" type="text"
                                   ng-model="ctrl.MainObj.AccDesc" class="form-control">
                            <span ng-show="mForm.Name.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                            <span ng-show="mForm.Name.$valid" class="text-success bi-check-lg float-end m-2"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" ng-show="ctrl.DlFalg == 0">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal" ng-click="ctrl.Save()">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
            <div class="modal-footer" ng-show="ctrl.DlFalg == 1">
                <button type="button" class="btn btn-primary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.d074003()"
                        ng-disabled="mForm.$invalid" data-bs-dismiss="modal">
                    تعديل
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="Dl_Message" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title col-11" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close float-end col-1" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-auto">
                        <h4 class="m-4 text-danger">{{ctrl.DlMessage}}</h4>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.d074004()"
                        data-bs-dismiss="modal">
                    نعم
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">لا</button>
            </div>
        </div>
    </div>
</div>
