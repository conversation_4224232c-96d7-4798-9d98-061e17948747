<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="a77e57150aba4a35ba66bbc9faae03c8" Name="Diagram1" ZoomLevel="82">
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgenCat" Width="1.5" PointX="3" PointY="39.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgencySystems" Width="1.5" PointX="0.75" PointY="4.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgUsers" Width="1.5" PointX="2.75" PointY="4.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Balance" Width="1.5" PointX="7.5" PointY="32.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CarsBrand" Width="1.5" PointX="4.75" PointY="4.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Cities" Width="1.5" PointX="3" PointY="52.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Colors" Width="1.5" PointX="5.75" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Comp_Agency" Width="1.5" PointX="10.5" PointY="39.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CompulsoryInsurenceTB" Width="1.5" PointX="3" PointY="21.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CompulsoryPriceDetails" Width="1.5" PointX="6.75" PointY="4.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CompulsoryPriceMaster" Width="1.5" PointX="7.75" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CoNews" Width="1.5" PointX="7.5" PointY="56.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Country" Width="1.5" PointX="0.75" PointY="25.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.DurationsCat" Width="1.5" PointX="0.75" PointY="1.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.DuratuionTB" Width="1.5" PointX="3" PointY="1" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.GivenSearils" Width="1.5" PointX="9.75" PointY="44.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.PermissionsList" Width="1.5" PointX="5.25" PointY="27.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.PrintedSerals" Width="1.5" PointX="8.75" PointY="4.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.PrinterTB" Width="1.5" PointX="8.75" PointY="7.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.ResAgencies" Width="1.5" PointX="7.5" PointY="39.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Seariles" Width="1.5" PointX="7.5" PointY="52.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SearStatus" Width="1.5" PointX="7.5" PointY="49.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SerialNums" Width="1.5" PointX="9.75" PointY="49.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Systems" Width="1.5" PointX="0.75" PointY="9.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.UserPermissions" Width="1.5" PointX="7.5" PointY="28.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Users" Width="1.5" PointX="5.25" PointY="30.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.UsersSearils" Width="1.5" PointX="7.5" PointY="61.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Virebles" Width="1.5" PointX="2.75" PointY="9.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Cities_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_DuratuionTB_DurationsCat" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_GivenSearils_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UserPermissions_PermissionsList" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_ResAgencies_Users1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialNums_SearStatus" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialNums_UsersSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UserPermissions_Users" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_CarsBrand" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Colors" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_CompulsoryPriceDetails" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_CompulsoryPriceMaster" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Balance" Width="1.5" PointX="7.875" PointY="43.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Cities" Width="1.5" PointX="14" PointY="5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocReNew" Width="1.5" PointX="10" PointY="11.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocTax" Width="1.5" PointX="10" PointY="21.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocumentType" Width="1.5" PointX="12.75" PointY="13.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DurationTypes" Width="1.5" PointX="14.75" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_GivenSearils" Width="1.5" PointX="13.125" PointY="51.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_PrintedSerals" Width="1.5" PointX="2.75" PointY="14.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_PrinterTB" Width="1.5" PointX="14.75" PointY="8.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Questions" Width="1.5" PointX="14.75" PointY="11.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Seariles" Width="1.5" PointX="7.875" PointY="66" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_SearStatus" Width="1.5" PointX="13.125" PointY="36.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_SerialNums" Width="1.5" PointX="15.375" PointY="35" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_UsersSearils" Width="1.5" PointX="15.375" PointY="28.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Virebles" Width="1.5" PointX="4.75" PointY="15.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Zoon" Width="1.5" PointX="14.75" PointY="15.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_AgUsers1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_UsersSearils_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Cities" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_GivenSearils_Trav_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_UsersSearils_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Trav_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Trav_SearStatus" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_Country" Width="1.5" PointX="11.75" PointY="4.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_Cities_Trav_Country" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.ConMangUsers" Width="1.5" PointX="19.625" PointY="1" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.ContryMangments" Width="1.5" PointX="17.375" PointY="1.25" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_ConMangUsers_ContryMangments" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.DocTypes" Width="1.5" PointX="1.125" PointY="29.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.NotesTB" Width="1.5" PointX="5.625" PointY="49.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.oldOwner" Width="1.5" PointX="5.625" PointY="23.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orang_Country" Width="1.5" PointX="0.75" PointY="17.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.OtherServHis" Width="1.5" PointX="5.625" PointY="20.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.OtherSettings" Width="1.5" PointX="16.75" PointY="4.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_DocTypes" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_NotesTB_CompulsoryInsurenceTB" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_oldOwner_CompulsoryInsurenceTB" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_OtherServHis_CompulsoryInsurenceTB" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_OtherServHis_CompulsoryInsurenceTB1" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.OrangeCars" Width="1.5" PointX="17.375" PointY="14.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Country_Condition" Width="1.5" PointX="17.375" PointY="18.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.VehicleNationality" Width="1.5" PointX="19.375" PointY="5.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Offices" Width="1.5" PointX="19.375" PointY="9.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocTax_Virebles" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_MaritalStatus" Width="1.5" PointX="21.375" PointY="9.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Nationality" Width="1.5" PointX="21.375" PointY="13.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Profession" Width="1.5" PointX="21.375" PointY="17.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Seariles" Width="1.5" PointX="7.5" PointY="71.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_SearStatus" Width="1.5" PointX="13.125" PointY="41.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_SerialNums" Width="1.5" PointX="15.375" PointY="40.5" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_UsersSearils" Width="1.5" PointX="18.375" PointY="31.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_Insurance_Clause" Width="1.5" PointX="19.375" PointY="25.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_Insurance_Policy" Width="1.5" PointX="21.625" PointY="22.375" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_poli_Nots" Width="1.5" PointX="23.875" PointY="26.125" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_AgUsers1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_UsersSearils_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_MidcRes_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_MidcRes_SearStatus" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_UsersSearils_Trav_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_Orang_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_Orange_Insurance_Clause" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_OrangeCars" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_VehicleNationality" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_poli_Nots_Orange_Insurance_Policy" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_GivenSearils" Width="1.5" PointX="9.75" PointY="54.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_PrintedSerals" Width="1.5" PointX="12.375" PointY="19.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_PrinterTB" Width="1.5" PointX="14.375" PointY="19.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_GivenSearils_MidcResSeariles" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidcRes_Address" Width="1.5" PointX="21.375" PointY="5.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Orange_nots_replays" Width="1.5" PointX="26.125" PointY="26.375" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_nots_replays_Orange_poli_Nots" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgencyPermission" Width="1.5" PointX="26.625" PointY="22" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_OtherSettings_Systems" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_InsuranceDocument" Width="1.5" PointX="24" PointY="14.5" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReNew_Trav_InsuranceDocument" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReNew_Trav_InsuranceDocument1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReNew_Trav_InsuranceDocument2" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocTax_Trav_InsuranceDocument" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Trav_DurationTypes" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Trav_Zoon" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidRes_AcademicQualification" Width="1.5" PointX="21.375" PointY="33.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidRes_Percentage" Width="1.5" PointX="12.375" PointY="22.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Smart_page_Cus" Width="1.5" PointX="14.375" PointY="22.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_DocReplacment" Width="1.5" PointX="26.25" PointY="15.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_OtherServPrices" Width="1.5" PointX="7.375" PointY="16.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReplacment_Trav_InsuranceDocument" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_DocReplacment_Trav_InsuranceDocument1" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Trav_AgesTypes" Width="1.5" PointX="9.375" PointY="17" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Trav_AgesTypes" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgenSysProfit" Width="1.5" PointX="10.5" PointY="25.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgPermissionsList" Width="1.5" PointX="24.375" PointY="30.25" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Cus_Companies" Width="1.5" PointX="16.375" PointY="23" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.MidRes_InsuranceDocument" Width="1.5" PointX="28.625" PointY="11.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyPermission_AgPermissionsList" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgenSysProfit_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompID" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Cus_Companies" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_Address" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_MaritalStatus" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_Nationality" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidRes_AcademicQualification" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Agency" Width="1.5" PointX="26.625" PointY="30.125" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_AgenCat" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_Cities" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_ContryMangments" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_Country" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyPermission_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgenSysProfit_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgUsers_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Balance_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Comp_Agency_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CompulsoryInsurenceTB_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CoNews_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidcRes_UsersSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Orange_Insurance_Policy_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_ResAgencies_Agency1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_Balance_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_InsuranceDocument_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Trav_UsersSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UsersSearils_Agency" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.monthlyAgenFinnce" Width="1.5" PointX="23.375" PointY="0.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.EquipmentCategories" Width="1.5" PointX="26.625" PointY="46.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgencyEquipment" Width="1.5" PointX="28.875" PointY="37.375" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyEquipment_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_AgencyEquipment_Category" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_UsersSearils" Width="1.5" PointX="28.875" PointY="23.5" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_UsersSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_UsersSearils_AgUsers" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_PrintedSerals" Width="1.5" PointX="30.875" PointY="19" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_PrintedSerals_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_PrintedSerals_AgUsers" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.RequestTypes" Width="1.5" PointX="31.625" PointY="9.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SerialHistory" Width="1.5" PointX="31.875" PointY="29.125" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.SerialStatuses" Width="1.5" PointX="31.625" PointY="14" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.StockInventory" Width="1.5" PointX="7.5" PointY="11.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.StockReceipts" Width="1.5" PointX="36.125" PointY="25.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.StockRequestDetails" Width="1.5" PointX="39.125" PointY="25.75" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.UnifiedRequests" Width="1.5" PointX="33.875" PointY="19.375" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_AgUsers_RequestedBy" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_RequestTypes" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_SerialStatuses_New" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_SerialStatuses_Previous" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_UnifiedRequests" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_SerialHistory_Users" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_SerialStatuses_Current" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_SerialStatuses_Requested" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockInventory_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockInventory_Users" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockReceipts_UnifiedRequests" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockReceipts_Users_GeneratedBy" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockRequestDetails_UnifiedRequests" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_StockRequestDetails_Users_AssignedBy" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_Systems" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_Users_AssignedBy" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_UnifiedRequests_Users_ReviewedBy" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CustodyReceipts" Width="1.5" PointX="28.875" PointY="43.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CustodyReceipts_Agency" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.CustodyReceiptDetails" Width="1.5" PointX="31.125" PointY="44.125" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CustodyReceiptDetails_ReceiptID" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_CustodyReceiptDetails_AgencyEquipment" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_MidRes_InsuranceDocument_MidcRes_Profession" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_GivenSearils" Width="1.5" PointX="31.125" PointY="36.625" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_Seariles" Width="1.5" PointX="28.875" PointY="32.875" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_SearStatus" Width="1.5" PointX="28.875" PointY="28.625" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_GivenSearils_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_Seariles_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_GivenSearils_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_UsersSearils_GivenSearils" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.Oran_SerialNums" Width="1.5" PointX="36.375" PointY="19.75" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_Agency" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_AgUsers" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_GivenSearils" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_Seariles" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Oran_SerialNums_Oran_SearStatus" />
        <EntityTypeShape EntityType="Insh_AppsDBModel.AgenMainAcc" Width="1.5" PointX="10.375" PointY="1" />
        <AssociationConnector Association="Insh_AppsDBModel.FK_Agency_AgenMainAcc" />
        </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>