@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="row justify-content-center">
    <div class="col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12 softBlue">
        <div class="card  navglassColor ">
            <div class="card-header">

                <div class=" cairo btn-group" dir="ltr" role="group" aria-label="Basic mixed styles example">
                    <button type="button" disabled class="btn btn-primary"> واجهة الالوان </button>
                    <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-primary"> <span class="m-2">واجهة التأمين الاجباري</span></button>
                    <button type="button" ng-click="ctrl.Home()" class="btn btn-success">
                        الشاشة الرئيسية
                        <i class="bi bi-house-fill"></i>
                    </button>
                </div>
                <div class=" float-end">

                    <button type="button"
                            class="btn btn-success  cairo  "
                            data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.AddNew()">
                        إضافة لون
                        <i class="bi bi-palette me-1"></i>
                    </button>

                </div>
            </div>
        </div>

        <div class="card-body">

            <div class="row float-right">
                <div class="col-sm-12 col-xs-12  offset-1 col-md-5 col-lg-5 col-xl-5 col-xxl-5 float-right">
                    <input class="inputStyleWhite cairo" type="text" ng-model="searchColor" placeholder="    بحث بإسم اللون ...  ">
                </div>
            </div>
            <div class="row  mt-4 cairo" style="height:90vh; padding:15px;overflow-y:scroll">
                <div class="table-responsive">
                    <table class="table  table-striped table-hover">
                        <tr class="bg-dark text-white">
                            <th class="text-center">#</th>
                            <th class="text-center"> اسم اللون </th>
                            <th class="text-center">  اسم اللون باللغة الانجليزية </th>
                            <th class="text-center"> المستخدم</th>
                            <th class="text-center"> تاريخ الإنشاء</th>


                            <th class="text-center">الحالة</th>
                            <th class="text-center" colspan="6">العمليات</th>
                        </tr>
                        <tr class="navglassColor text-white"
                            ng-repeat="x in ctrl.ObjColor | filter:searchColor">
                            <td class="text-center">{{$index + 1}}</td>
                            <td class="text-center">{{x.col_Name}}</td>
                            <td class="text-center">{{x.col_NameEN}}</td>
                            <td class="text-center">{{x.InsertBy}}</td>
                            <td class="text-center">{{x.InsertDate}}</td>
                            <td class="text-center">{{x.Status == 1 ?   "مفعل" : "موقوف"}}</td>

                            <td>
                                <button type="button" class="btn btn-primary" ng-click="ctrl.BtnMainEdit(x)">
                                    تعديل
                                    <i class="fa fa-edit"></i>
                                </button>
                            </td>
                            <td ng-show="x.Status == 1">
                                <button type="button" class="btn btn-warning" ng-click="ctrl.BtnStatus(x,2)">
                                    إيقاف
                                    <i class="fa fa-toggle-off"></i>
                                </button>
                            </td>
                            <td ng-show="x.Status == 2">
                                <button type="button" class="btn btn-success" ng-click="ctrl.BtnStatus(x,1)">
                                    تفعيل
                                    <i class="fa fa-toggle-on"></i>

                                </button>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger" ng-click="ctrl.BtnStatus(x,3)">
                                    حذف
                                    <i class="fa fa-trash"></i>
                                </button>
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>

<div class="modal fade" id="Dl_Message" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-auto">
                        <h4 class="m-4 text-danger">{{ctrl.DlMessage}}</h4>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a20006()"
                        data-bs-dismiss="modal">
                    نعم
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">لا</button>
            </div>

        </div>
    </div>
</div>


 
    <div class="modal fade" id="NewItem"    aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
        <div class="modal-dialog ">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title col-11" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                    <button type="button" class="btn-close float-end col-1" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form name="mForm" class="row g-3" autocomplete="off" novalidate>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 col-xl-12 col-xl-10 ">
                            <div class="col-auto m-6">
                                <label for="col_Name">اسم اللون</label>
                                <input required name="col_Name" type="text"
                                       ng-model="ctrl.MainObj.col_Name" class="form-control ">
                                <span ng-show="mForm.ColorNaame.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>

                                <span ng-show="mForm.col_Name.$valid" class="text-success bi-check-lg float-end m-2"></span>

                            </div>
                            <div class="col-auto m-6">
                                <label for="col_NameEN">Color Name Engish</label>
                                <input required name="col_NameEN" type="text"
                                       ng-model="ctrl.MainObj.col_NameEN" class="form-control ">
                                <span ng-show="mForm.ColorNameEN.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>

                                <span ng-show="mForm.ColorNameEN.$valid" class="text-success bi-check-lg float-end m-2"></span>

                            </div>



                        </div>

                    </form>
                </div>
                <div class="modal-footer" ng-show=" ctrl.DlFalg == 0">
                    <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal" ng-click="ctrl.Save()">
                        حفظ
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                </div>
                <div class="modal-footer" ng-show=" ctrl.DlFalg == 1">
                    <button type="button" class="btn btn-primary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4"  ng-click="ctrl.a20005()"
                            ng-disabled="mForm.$invalid " data-bs-dismiss="modal">
                        تعديل
                    </button>
                    <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
                </div>
            </div>
        </div>
    </div>
