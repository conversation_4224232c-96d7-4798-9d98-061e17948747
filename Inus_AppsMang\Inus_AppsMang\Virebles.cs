//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Inus_AppsMang
{
    using System;
    using System.Collections.Generic;
    
    public partial class Virebles
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Virebles()
        {
            this.Trav_DocTax = new HashSet<Trav_DocTax>();
        }
    
        public System.Guid VirD { get; set; }
        public string VirDesc { get; set; }
        public Nullable<double> Value { get; set; }
        public string InsertBy { get; set; }
        public Nullable<System.DateTime> InsertDate { get; set; }
        public string UpdateBy { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public Nullable<System.Guid> ConntryID { get; set; }
        public Nullable<bool> IsPer { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<bool> IsWithProfitMargin { get; set; }
        public Nullable<double> IsrounsdedTo { get; set; }
        public string Eng_Name { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trav_DocTax> Trav_DocTax { get; set; }
    }
}
