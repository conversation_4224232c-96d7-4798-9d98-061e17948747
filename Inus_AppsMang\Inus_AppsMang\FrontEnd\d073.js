(function () {
    'use strict';
    angular
        .module('App')
        .controller('d073', ['$scope', 'DataService', '$state', '$stateParams', 'Notification', d073]);

    function d073($scope, DataService, $state, $stateParams, Notification) {
        var ctrl = this;
        ctrl.QualificationList = [];
        ctrl.MainObj = {};
        ctrl.DlFalg = 0;
        ctrl.Title = "";
        ctrl.DlTitle = "";
        ctrl.DlMessage = "";
        ctrl.SelectedID = "";
        ctrl.Status = 0;

        ctrl.Medical_HomePage = Medical_HomePage;
        ctrl.Home = Home;
        ctrl.AddNew = AddNew;
        ctrl.Save = Save;
        ctrl.BtnMainEdit = BtnMainEdit;
        ctrl.d073003 = d073003;
        ctrl.BtnStatus = BtnStatus;
        ctrl.d073004 = d073004;
        // Session Management
        vm.initializeSession = function () {
            var sessionToken = localStorage.getItem('sessionToken');
            var userID = localStorage.getItem('userID');
            var userName = localStorage.getItem('userName');

            if (!sessionToken || !userID) {
                $state.go('LoginPage');
                return false;
            }

            blockUI.start();
            return DataService.a29002(sessionToken)
                .then(function (response) {
                    var data = response.data;
                    if (data.ErrorCode === 0) {
                        vm.UserID = data.UserID;
                        vm.UserName = data.UserName;
                        vm.sessionToken = sessionToken;
                        activate();
                        return DataService.a29004(sessionToken);
                    } else {
                        vm.clearSessionData();
                        $state.go('LoginPage');
                        return false;
                    }
                })
                .then(function (response) {
                    if (response && response.data.ErrorCode === 0) {
                        if (!response.data.Perms.med_percentage) {
                            Notification.error({
                                message: "لا تملك الصلاحيات المناسبة للوصول إلى هذه الصفحة",
                                delay: 2000,
                                positionY: "bottom",
                                positionX: "center"
                            });
                            vm.sys_page();
                            return false;
                        }
                        return true;
                    }
                    return false;
                })
                .catch(function () {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    return false;
                })
                .finally(function () {
                    blockUI.stop();
                });
        };

        vm.initializeSession();
        vm.clearSessionData = function () {
            localStorage.removeItem('sessionToken');
            localStorage.removeItem('userID');
            localStorage.removeItem('userName');
        };


        function activate() {
            DataService.d073001($stateParams.UserID).then(function (response) {
                ctrl.QualificationList = response.data;
            });
        }

        function Medical_HomePage() {
            $state.go('Medical_HomePage', {
                UserID: $stateParams.UserID,
                UserName: $stateParams.UserName
            });
        }

        function Home() {
            $state.go('HomePage', {
                UserID: $stateParams.UserID,
                UserName: $stateParams.UserName
            });
        }

        function AddNew() {
            ctrl.MainObj = {};
            ctrl.DlFalg = 0;
            ctrl.Title = "إضافة مؤهل علمي جديد";
            ctrl.DlTitle = "إضافة مؤهل علمي جديد";
        }

        function Save() {
            if (ctrl.MainObj.AcademicQualificationName && ctrl.MainObj.Price) {
                DataService.d073002(ctrl.MainObj, $stateParams.UserID).then(function (response) {
                    if (response.data.error) {
                        Notification.error({ message: response.data.error, delay: 2000 });
                    } else {
                        Notification.success({ message: 'تم الحفظ بنجاح', delay: 2000 });
                        activate();
                    }
                });
            } else {
                Notification.error({ message: 'يرجى إدخال جميع البيانات المطلوبة', delay: 2000 });
            }
        }

        function BtnMainEdit(obj) {
            ctrl.MainObj = {
                AcademicQualificationID: obj.AcademicQualificationID,
                AcademicQualificationName: obj.AcademicQualificationName,
                Price: obj.Price
            };
            ctrl.DlFalg = 1;
            ctrl.Title = "تعديل مؤهل علمي";
            ctrl.DlTitle = "تعديل مؤهل علمي";
        }

        function d073003() {
            if (ctrl.MainObj.AcademicQualificationName && ctrl.MainObj.Price) {
                DataService.d073003(ctrl.MainObj, $stateParams.UserID).then(function (response) {
                    if (response.data.error) {
                        Notification.error({ message: response.data.error, delay: 2000 });
                    } else {
                        Notification.success({ message: 'تم التعديل بنجاح', delay: 2000 });
                        activate();
                    }
                });
            } else {
                Notification.error({ message: 'يرجى إدخال جميع البيانات المطلوبة', delay: 2000 });
            }
        }

        function BtnStatus(obj, status) {
            ctrl.SelectedID = obj.AcademicQualificationID;
            ctrl.Status = status;
            ctrl.DlTitle = status === 1 ? "تفعيل مؤهل علمي" :
                          status === 2 ? "إيقاف مؤهل علمي" :
                          "حذف مؤهل علمي";
            ctrl.DlMessage = status === 1 ? "هل تريد تفعيل المؤهل العلمي؟" :
                           status === 2 ? "هل تريد إيقاف المؤهل العلمي؟" :
                           "هل تريد حذف المؤهل العلمي؟";
        }

        function d073004() {
            DataService.d073004(ctrl.SelectedID, ctrl.Status, $stateParams.UserID).then(function (response) {
                if (response.data.error) {
                    Notification.error({ message: response.data.error, delay: 2000 });
                } else {
                    var message = ctrl.Status === 1 ? 'تم التفعيل بنجاح' :
                                 ctrl.Status === 2 ? 'تم الإيقاف بنجاح' :
                                 'تم الحذف بنجاح';
                    Notification.success({ message: message, delay: 2000 });
                    activate();
                }
            });
        }
    }
})(); 