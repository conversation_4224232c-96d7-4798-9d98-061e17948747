﻿<link href="~/Content/StyleSheet1.css" rel="stylesheet" media="screen" />
<style>
    /* Fix dropdown positioning issues */
    .dropdown-menu {
        position: absolute !important;
        z-index: 9999 !important;
        max-height: 70vh !important;
        overflow-y: auto !important;
        min-width: 200px !important;
        border: 1px solid #dee2e6 !important;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        background-color: white !important;
        margin-top: 5px !important;
    }
    
    /* Navbar container fixes */
    .navbar {
        overflow: visible !important;
        position: relative !important;
        z-index: 1050 !important;
        margin-bottom: 20px !important;
    }
    
    /* Dropdown container fixes */
    .dropdown {
        position: relative !important;
    }
    
    /* Ensure dropdowns appear above cards and other content */
    .card {
        position: relative !important;
        z-index: 1 !important;
    }
    
    /* Page container adjustments */
    .row.pt-4 {
        margin-top: 20px !important;
        position: relative !important;
        z-index: 1 !important;
    }
    
    /* Force dropdown visibility when shown */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translate3d(0px, 0px, 0px) !important;
    }
    
    /* Bootstrap dropdown positioning override */
    .dropdown-menu[data-bs-popper] {
        position: absolute !important;
        z-index: 9999 !important;
    }
    
    /* Ensure proper spacing between navbar and content */
    .navbar + hr {
        margin: 20px 0 !important;
    }
</style>
<hr />
<div class="row justify-content-center " style="min-height:100vh ">
    <div class="col-lg-12 col-md-12 col-xl-12 col-xxl-12 col-sm-12 col-xs-12  pt-2 pb-3">
        <nav class="navbar navbar-expand-sm cairo border-1 px-1 d-flex justify-content-between" style="background-color: darkgray; position: relative; z-index: 1000;" ng-if="ctrl.ShowFlag==0">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarScroll"
                    aria-controls="navbarScroll" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Admin dropdown - Left side -->
            <div class="dropdown" style="position: relative;">
                <button class="btn btn-primary dropdown-toggle m-1" type="button" id="dropdownMenuButtonAdmin" 
                        data-bs-toggle="dropdown" data-bs-offset="0,10" data-bs-placement="bottom-start"
                        dir="ltr" aria-expanded="false" data-bs-auto-close="true">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-justify" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M2 12.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5m0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5m0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5m0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5" />
                    </svg>
                    <span class="" style="font-size:medium;color:white">الإدارة العامة</span>
                </button>
                <ul class="dropdown-menu cairo text-center" aria-labelledby="dropdownMenuButtonAdmin" dir="ltr">
                                         <li ng-if="ctrl.Perms.MainAcc"><a class="dropdown-item" ng-click="ctrl.MainAccountPage()" href="">إدارة الحساب الرئيسي</a></li>
                    <li ng-if="ctrl.Perms.agency"><a class="dropdown-item" ng-click="ctrl.AgencyPage()" href="">الوكلاء</a> </li>
                    @*<li ng-if="ctrl.Perms.palance"><a class="dropdown-item" ng-click="ctrl.BtnLocalAgency()" href="">واجهة حسبات الوكلاء</a></li>*@
                    <li><a class="dropdown-item" ng-click="ctrl.ContractTerminationPage()" href="">إنهاء التعاقد مع الوكلاء</a></li>
                    <li ng-if="ctrl.Perms.palance"><a class="dropdown-item" ng-click="ctrl.BtnAprrovDel()" href="">قبول طلبات الحذف  </a></li>
                    <li ng-if="ctrl.Perms.users"><a class="dropdown-item" ng-click="ctrl.UsersPage() " href="">المستخدمين</a> </li>
                    <li ng-if="ctrl.Perms.news"><a class="dropdown-item" ng-click="ctrl.BtnNews()" href="">الاخبار</a></li>
                    <li ng-if="ctrl.Perms.news"><a class="dropdown-item" ng-click="ctrl.Sittings()" href="">الاعدادات</a></li>
                </ul>
            </div>

            <div class="collapse navbar-collapse text-center" id="navbarScroll" ng-if="ctrl.ShowFlag==0">
                <ul class="navbar-nav me-auto my-2 my-lg-0 navbar-nav-scroll text-center " style="--bs-scroll-height: 100px; width: 90% !important;">
                    <li class="nav-item " style="width:100% !important">
                        <div class="d-flex justify-content-between align-items-center  " style="background-color: white">
                            <div class="d-flex flex-row flex-grow-1 flex-fill justify-content-center
                                            py-2  text-white px-1 news " style="background-color:#ae0000">
                                <span class="d-flex align-items-center cairo">&nbsp;الأخبار</span>
                            </div>
                            <marquee class="news-scroll" behavior="scroll" direction="right" onmouseover="this.stop();" onmouseout="this.start();">
                                <a ng-repeat="x in ctrl.News">{{x.Title}} : {{x.NewsText}} <span class="dot"></span> </a>
                                <span ng-if="!ctrl.News || ctrl.News.length === 0">لا توجد أخبار جديدة</span>
                            </marquee>
                        </div>
                    </li>
                </ul>
            </div>

            <!-- User dropdown - Right side -->
            <div class="dropdown" style="position: relative;">
                <button class="btn btn-danger dropdown-toggle m-1" type="button" id="dropdownMenuButton2" 
                        data-bs-toggle="dropdown" data-bs-offset="-50,10" data-bs-placement="bottom-end"
                        dir="ltr" aria-expanded="false" data-bs-auto-close="true">
                    <span class="" style="font-size:medium;color:white">
                        {{ctrl.UserName}}
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-gear" viewBox="0 0 16 16">
                            <path d="M11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0M8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m.256 7a4.5 4.5 0 0 1-.229-1.004H3c.001-.246.154-.986.832-1.664C4.484 10.68 5.711 10 8 10q.39 0 .74.025c.226-.341.496-.65.804-.918Q8.844 9.002 8 9c-5 0-6 3-6 4s1 1 1 1zm3.63-4.54c.18-.613 1.048-.613 1.229 0l.043.148a.64.64 0 0 0 .921.382l.136-.074c.561-.306 1.175.308.87.869l-.075.136a.64.64 0 0 0 .382.92l.149.045c.612.18.612 1.048 0 1.229l-.15.043a.64.64 0 0 0-.38.921l.074.136c.305.561-.309 1.175-.87.87l-.136-.075a.64.64 0 0 0-.92.382l-.045.149c-.18.612-1.048.612-1.229 0l-.043-.15a.64.64 0 0 0-.921-.38l-.136.074c-.561.305-1.175-.309-.87-.87l.075-.136a.64.64 0 0 0-.382-.92l-.148-.045c-.613-.18-.613-1.048 0-1.229l.148-.043a.64.64 0 0 0 .382-.921l-.074-.136c-.306-.561.308-1.175.869-.87l.136.075a.64.64 0 0 0 .92-.382zM14 12.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0" />
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end cairo" aria-labelledby="dropdownMenuButton2" dir="ltr">
                    <li>
                        <a class="dropdown-item text-center" ng-click="ctrl.UpdatePasswordDl()" href="">
                            <i class="bi bi-key me-2"></i>تعديل كلمة المرور
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item text-center" ng-click="ctrl.logout()" href="">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل خروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>


        <hr />


        <div class="row pt-4" ng-if="ctrl.ShowFlag==0">

            <div class="col-md-4" ng-if="ctrl.Perms.InsuranceInterfacemanagmentSys">

                <div class="card " style="min-height:200px !important">
                    <button class="btn btn-outline-secondary full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="ctrl.a270059()">

                        <div class="col  d-flex justify-content-center ">
                            <img class="card-img svgStyle2" ng-src="~/Imges/business_people_animation.gif" />
                        </div>
                        <div class="col justify-content-center">
                            <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">    نظام إدارة واجهات التأمين   </p>



                        </div>
                    </button>

                </div>



            </div>

            <div class="col-md-4" ng-if="ctrl.Perms.agency_sys">

                <div class="card " style="min-height:200px !important">
                    <button class="btn btn-outline-dark full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="ctrl.a270060()">

                        <div class="col  d-flex justify-content-center ">
                            <img class="card-img svgStyle2" ng-src="~/Imges/animated_announcement.gif" />
                        </div>
                        <div class="col justify-content-center">
                            <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">    نظام متابعة الوكلاء   </p>



                        </div>
                    </button>

                </div>


            </div>

            <div class="col-md-4" ng-if="ctrl.Perms.finance_sys">

                <div class="card " style="min-height:200px !important">
                    <button class="btn btn-outline-secondary full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="">

                        <div class="col  d-flex justify-content-center ">
                            <img class="card-img svgStyle2" ng-src="~/Imges/ChatGPT Image Apr 14, 2025, 11_42_59 PM.png" />
                        </div>
                        <div class="col justify-content-center">
                            <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">    نظام المالية   </p>



                        </div>
                    </button>

                </div>


            </div>

            <!-- نظام إنهاء التعاقد مع الوكلاء -->
            <div class="col-md-4" ng-if="ctrl.Perms.agincu_contractEnd">
                <div class="card " style="min-height:200px !important">
                    <button class="btn btn-outline-danger full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="ctrl.ContractTerminationPage()">
                        <div class="col  d-flex justify-content-center ">
                            <img class="card-img svgStyle2" ng-src="~/Imges/animated_announcement.gif" />
                        </div>
                        <div class="col justify-content-center">
                            <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">إنهاء التعاقد مع الوكلاء</p>
                            <p class="card-text small text-muted cairo" style="text-align:center">بحث وتحديد الوكلاء لإنهاء التعاقد</p>
                        </div>
                    </button>

                </div>

            </div>
        </div>
        <div ng-if="ctrl.ShowFlag == 6">
            <div class="row">
                <div class="card  navglassColor ">
                    <div class="card-header">
                        <div class=" cairo btn-group" dir="ltr" role="group" aria-label="Basic mixed styles example">
                            <button type="button" disabled class="btn btn-primary"> نظام متابعة الوكلاء</button>
                            <button class="btn btn-primary   " ng-click="ctrl.ShowFlag = 0">
                                أنظمة الإدارة
                                <i class="bi bi-house-fill"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-4" ng-if="ctrl.Perms.compolsy_Sys">

                    <div class="card " style="min-height:200px !important">
                        <button class="btn btn-outline-dark full-size cairo border-0 full-size" style="min-height:200px !important"  
                                ng-click="ctrl.BtnLocalAgency()" >

                            <div class="col   ">
                                <img class="card-img svgStyle2" ng-src="~/Imges/fed384bf87aa536f37ab0380d0e6ee5c.jpg" />
                            </div>

                            <div class="col">
                                <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">   واجهة حسبات الوكلاء   </p>




                            </div>
                        </button>
                    </div>

                </div>
            </div>

        </div>

        <div class="" ng-if="ctrl.ShowFlag==3">
            <div class="row">
                <div class="card  navglassColor ">
                    <div class="card-header">
                        <div class=" cairo btn-group" dir="ltr" role="group" aria-label="Basic mixed styles example">
                            <button type="button" disabled class="btn btn-primary"> واجهة إدارة أنظمة التأمين</button>
                            <button class="btn btn-primary   " ng-click="ctrl.ShowFlag = 0">
                                أنظمة الإدارة
                                <i class="bi bi-house-fill"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-4" ng-if="ctrl.Perms.compolsy_Sys">

                    <div class="card " style="min-height:200px !important">
                        <button class="btn btn-outline-primary full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="ctrl.Com_HomePage()">

                            <div class="col   ">
                                <img class="card-img svgStyle2" ng-src="~/Imges/1.gif" />
                            </div>

                            <div class="col">
                                <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">    نظام تأمين الإجباري   </p>




                            </div>
                        </button>
                    </div>

                </div>
                <div class="col-md-4" ng-if="ctrl.Perms.traviles_Sys">

                    <div class="card" style="min-height:200px !important">
                        <button class="btn btn-outline-success full-size cairo border-0 full-size " style="min-height:200px !important" ng-click="ctrl.Tarv_HomePage()">
                            <div class="col  d-flex justify-content-center ">
                                <img class="card-img svgStyle2" ng-src="~/Imges/2.gif" />
                            </div>

                            <div class="col">
                                <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">    نظام تأمين المسافرين   </p>



                            </div>
                        </button>

                    </div>

                </div>
                <div class="col-md-4" ng-if="ctrl.Perms.orang_sys">

                    <div class="card " style="min-height:200px !important">
                        <button class="btn btn-outline-warning full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="ctrl.OrnagePage()">

                            <div class="col  d-flex justify-content-center ">
                                <img class="card-img svgStyle2" ng-src="~/Imges/Trip.gif" />
                            </div>
                            <div class="col justify-content-center">
                                <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">    نظام تأمين البرتقالية   </p>



                            </div>
                        </button>

                    </div>

                </div>
                <div class="col-2">

                </div>

                <div class="col-xs-6 col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4 " ng-if="ctrl.Perms.medical_sys">

                    <div class="card " style="min-height:200px !important">
                        <button class="btn btn-outline-danger full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="ctrl.Medical_HomePage()">

                            <div class="col  d-flex justify-content-center ">
                                <img class="card-img svgStyle2" ng-src="~/Imges/animated_healthcare.gif" />
                            </div>
                            <div class="col justify-content-center">
                                <p class="card-title fw-bold  text-darkBlue cairo" style="text-align:center">    نظام تأمين المسؤولية الطبية   </p>
                            </div>
                        </button>
                    </div>
                </div>

                <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4" @*ng-if="ctrl.Perms.inventory_a_requests"*@>

                    <div class="card " style="min-height:200px !important">

                        <button class="btn btn-outline-info full-size cairo border-0 full-size" style="min-height:200px !important" ng-click="ctrl.StockRequestsPage()">
                            <div class="col  d-flex justify-content-center  ">
                                <img class="card-img svgStyle" ng-src="~/Imges/67814683470823471e149b411f4a8152.jpg" />
                            </div>
                            <div class="col">
                                <p class="card-title fw-bold cairo text-darkBlue " style="text-align:center"> طلبات المخزون </p>
                            </div>
                        </button>

                    </div>

                </div>
                <div class="col-2">

                </div>
            </div>
        </div>


    </div>
   
</div>






<div class="modal fade" id="Sittings" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
         
                    <form name="FrmSett" class="row g-3 cairo" autocomplete="off" novalidate>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-repeat="x in ctrl.SettingObj">
                            <h4> {{x.sysName}}</h4>
                            <div class="col-auto m-4" ng-repeat="y in x.Settings">
                                <label for="Setting{{$parent.$index}}_{{$index}}">{{y.Label}}</label>
                                <input name="Setting{{$parent.$index}}_{{$index}}"
                                       required
                                       ng-model="y.Value"
                                       type="number"
                                       class="form-control text-center">
                                <span ng-show="FrmSett['Setting' + $parent.$index + '_' + $index].$error.required"
                                      class="text-danger float-end m-2">
                                    يجب إدخال هذا الحقل
                                </span>
                            </div>
                        </div>
                    </form>
              
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary cairo col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a27056()"
                        ng-disabled="FrmSett.$invalid " data-bs-dismiss="modal">
                    <i class="bi bi-check-lg me-2"></i>حفظ
                </button>
                <button type="button" class="cairo btn btn-danger col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-2"></i>إلغاء الأمر
                </button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="UpdatePasswordDl" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form name="FrmPass" class="row g-3 cairo" autocomplete="off" novalidate>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12 ">
                        <div class="col-auto m-4">
                            <label for="OldPassword"> كملة المرور القديمة</label>
                            <input name="OldPassword" pattern="^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.*\s).*$"
                                   required ng-model="ctrl.OldPassword" type="password" class="form-control">
                            <span ng-show="FrmPass.OldPassword.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                            <span ng-show="FrmPass.OldPassword.$error.minlength" class="text-danger float-end m-2">اقل عدد حروف  5</span>
                            <span ng-show="FrmPass.OldPassword.$error.pattern" class="text-danger float-end m-2">يجب أن يحتوي على حروف كبيرة و حروف صغيرة وأرقام</span>
                            <span ng-show="FrmPass.OldPassword.$valid" class="text-success bi-check-lg float-end m-2"></span>

                        </div>
                        <div class="col-auto m-4">
                            <label for="NewPassword">كملة المرور الجديدة</label>
                            <input name="NewPassword" pattern="^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.*\s).*$"
                                   required ng-model="ctrl.NewPassword" type="password" class="form-control">
                            <span ng-show="FrmPass.NewPassword.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                            <span ng-show="FrmPass.NewPassword.$error.minlength" class="text-danger float-end m-2">اقل عدد حروف  5</span>
                            <span ng-show="FrmPass.NewPassword.$error.pattern" class="text-danger float-end m-2">يجب أن يحتوي على حروف كبيرة و حروف صغيرة وأرقام</span>
                            <span ng-show="FrmPass.NewPassword.$valid" class="text-success bi-check-lg float-end m-2"></span>

                        </div>
                        <div class="col-auto m-4">
                            <label for="RePassword">تأكيد كلمة المرور</label>
                            <input ng-disabled="FrmPass.Password.$error.pattern" name="RePassword" required type="password" class="form-control" ng-model="ctrl.RePassword">
                            <span ng-show="FrmPass.RePassword.$error.required" class="text-danger float-end m-2">يجب إدخال هذا الحقل</span>
                            <span ng-show="(ctrl.NewPassword != ctrl.RePassword) && FrmPass.RePassword.$error.required == null " class="text-danger float-end m-2">كلمة المرور غير متطابقة</span>
                            <span ng-show="(ctrl.NewPassword == ctrl.RePassword && FrmPass.RePassword.$error.required  == null ) " class="text-success bi-check-lg float-end m-2"></span>

                        </div>

                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary cairo col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.a27015()"
                        ng-disabled="FrmPass.$invalid  || ctrl.NewPassword != ctrl.RePassword" data-bs-dismiss="modal">
                    <i class="bi bi-check-lg me-2"></i>حفظ
                </button>
                <button type="button" class="cairo btn btn-danger col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-2"></i>إلغاء الأمر
                </button>
            </div>

        </div>
    </div>
</div>

