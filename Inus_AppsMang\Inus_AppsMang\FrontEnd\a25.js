﻿(function () {
    'use strict';
   
    angular.module("App")
           .config(["$stateProvider", "$urlRouterProvider", '$locationProvider', function ($stateProvider, $urlRouterProvider, $locationProvider) {
              
               $urlRouterProvider.otherwise("LoginPage");
               $stateProvider               
                   .state('MidcRes_SerialsPage', {
                       url: "/MidcRes_SerialsPage",
                       templateUrl: "/d068/d680001",
                       controller: "d068",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('AcademicQualificationPage', {
                       url: "/AcademicQualificationPage",
                       templateUrl: "/d073/d073000",
                       controller: "d073",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('MainAccountPage', {
                       url: "/MainAccountPage",
                       templateUrl: "/d074/d074000",
                       controller: "d074",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('MaritalStatusPage', {
                       url: "/MaritalStatusPage",
                       templateUrl: "/d067/d67000",
                       controller: "d067",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('ProfessionPage', {
                       url: "/ProfessionPage",
                       templateUrl: "/d066/d66000",
                       controller: "d066",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('NationalityPage', {
                       url: "/NationalityPage",
                       templateUrl: "/d065/d65000",
                       controller: "d065",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('AddressPage', {
                       url: "/AddressPage",
                       templateUrl: "/d064/d64000",
                       controller: "d064",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Medical_HomePage', {
                       url: "/Medical_HomePage",
                       templateUrl: "/a27/a2700004",
                       controller: "a27Med",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Oran_SerialsPage', {
                       url: "/Oran_SerialsPage",
                       templateUrl: "/d071/d710001",
                       controller: "d071",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })

                   .state('Orange_SearilPage', {
                       url: "/Orange_SearilPage",
                       templateUrl: "/a70/a700000",
                       controller: "a70",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Orange_InsuranceClausePage', {
                       url: "/Orange_InsuranceClausePage",
                       templateUrl: "/d069/d0690001",
                       controller: "d069",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('OfficesPage', {
                       url: "/OfficesPage",
                       templateUrl: "/d063/d0630001",
                       controller: "d063",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Vehicle_NationalityPage', {
                       url: "/Vehicle_NationalityPage",
                       templateUrl: "/d062/d0620001",
                       controller: "d062",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Country_ConditionPage', {
                       url: "/Country_ConditionPage",
                       templateUrl: "/d061/d0610001",
                       controller: "d061",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Orange_CarPage', {
                       url: "/Orange_CarPage",
                       templateUrl: "/b059/b0590001",
                       controller: "b059",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                 
                   .state('Orange_CountryPage', {
                       url: "/Orange_CountryPage",
                       templateUrl: "/b058/b0580001",
                       controller: "b058",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_ZonePlace', {
                       url: "/Trav_ZonePlace",
                       templateUrl: "/a58/b0580001",
                       controller: "a58",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_DelApp', {
                       url: "/Trav_DelApp",
                       templateUrl: "/a57/a570001",
                       controller: "a57",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_RptMinistry', {
                       url: "/Trav_RptMinistry",
                       templateUrl: "/a56/a560005",
                       controller: "a56",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_RptFinancial', {
                       url: "/Trav_RptFinancial",
                       templateUrl: "/a56/a560004",
                       controller: "a56",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_ReportesPage', {
                       url: "/Trav_ReportesPage",
                       templateUrl: "/a56/a560003",
                       controller: "a56",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_HomeRpt', {
                       url: "/Trav_HomeRpt",
                       templateUrl: "/a56/a560002",
                       controller: "a56",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_balanreconrpt', {
                       url: "/Trav_balanreconrpt",
                       templateUrl: "/a56/a560001",
                       controller: "a56",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_SerialsPage', {
                       url: "/Trav_SerialsPage",
                       templateUrl: "/a55/a550001",
                       controller: "a55",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_BalnLocalPage', {
                       url: "/Trav_BalnLocalPage",
                       templateUrl: "/a54/a540001",
                       controller: "a54",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_AgencyPage', {
                       url: "/Trav_AgencyPage",
                       templateUrl: "/a53/a530001",
                       controller: "a53",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Trav_CountryPage', {
                       url: "/Trav_CountryPage",
                       templateUrl: "/a52/a52001",
                       controller: "a52",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Orange_HomePage', {
                       url: "/Orange_HomePage",
                       templateUrl: "/a27/a2700003",
                       controller: "a27Or",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Tarv_HomePage', {
                       url: "/Tarv_HomePage",
                       templateUrl: "/a27/a2700002",
                       controller: "a27Tr",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('Com_HomePage', {
                       url: "/Com_HomePage",
                       templateUrl: "/a27/a2700001",
                       controller: "a27Co",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('DetailReport', {
                       url: "/DetailReport",
                       templateUrl: "/a51/a510000",
                       controller: "a51",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })

                   .state('Reports_Home', {
                       url: "/Reports_Home",
                       templateUrl: "/a42/a420000",
                       controller: "a42",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('AgencyTraking_page', {
                       url: "/AgencyTraking_page",
                       templateUrl: "/a41/a41001",
                       controller: "a41",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('Insurnce_RPT', {
                       url: "/Insurnce_RPT",
                       templateUrl: "/a42/a420001",
                       controller: "a42",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('CompaniesPage', {
                       url: "/CompaniesPage",
                       templateUrl: "/a50/a500000",
                       controller: "a50",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
               
                   .state('DeletionApprovalPage', {
                       url: "/DeletionApprovalPage",
                       templateUrl: "/a49/a450001",
                       controller: "a49",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           Perms1: '',
                           Perms2: '',
                           Perms3: '',
                       }
                   })

                   .state('LoginDeleshinPage', {
                       url: "/LoginDeleshinPage",
                       templateUrl: "/a49/a450011",
                       controller: "a59",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                          
                       }
                   })
                   .state('ControllDeletePage', {
                       url: "/ControllDeletePage",
                       templateUrl: "/a49/a450021",
                       controller: "a60",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',

                       }
                   })
                   .state('SettingPage', {
                       url: "/SettingPage",
                       templateUrl: "/a46/a46000",
                       controller: "a46",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                          
                       }
                   })
                   .state('CarsPage', {
                       url: "/CarsPage",
                       templateUrl: "/a45/a45000",
                       controller: "a45",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                          
                       }
                   })
                   .state('AgencyPage', {
                       url: "/AgencyPage",
                       templateUrl: "/a40/a40000",
                       controller: "a40",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('AgencyEquipmentPage', {
                       url: "/AgencyEquipmentPage",
                       templateUrl: "/a59/a59000",
                       controller: "a59Controller",
                       controllerAs: "vm",
                       params: {
                           UserID: '',
                           UserName: '',
                           AgencyID: '',
                           AgencyName: ''
                       }
                   })
                   .state('HomeRpt', {
                       url: "/HomeRpt",
                       templateUrl: "/a44/a440004",
                       controller: "a44",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
        
             
                    .state('SerialsPage', {
                       url: "/SerialsPage",
                       templateUrl: "/a43/a43001",
                       controller: "a43",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
           
                   .state('NewsPage', {
                       url: "/NewsPage",
                       templateUrl: "/a31/a30000",
                       controller: "a31",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('ColorsPage', {
                       url: "/ColorsPage",
                       templateUrl: "/a20/a22001",
                       controller: "a20",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('CountryPage', {
                       url: "/CountryPage",
                       templateUrl: "/a22/a21001",
                       controller: "a22",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                    .state('UsersPage', {
                       url: "/UsersPage",
                       templateUrl: "/a26/a25001",
                       controller: "a26",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('HomePage', {
                       url: "/HomePage",
                       templateUrl: "/a27/a26001",
                       controller: "a27",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                           CompID: '',
                           ShowFlag:0
                       }
                   })

          
                   .state('LoginPage', {
                       url: "/LoginPage",
                       templateUrl: "/a27/a26000",
                       controller: "a29",
                       controllerAs: "ctrl",


                   })
                   .state('StockRequestsPage', {
                       url: "/StockRequestsPage",
                       templateUrl: "/a72/a72000",
                       controller: "a72",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('ContractTerminationPage', {
                       url: '/ContractTerminationPage',
                       templateUrl: '/a73/a73000',
                       controller: 'a73',
                       controllerAs: 'ctrl',
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('CustodyReceiptPage', {
                       url: '/CustodyReceiptPage',
                       templateUrl: '/a74/a74000',
                       controller: 'a74',
                       controllerAs: 'vm',
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('PercentagePage', {
                       url: '/PercentagePage',
                       templateUrl: '/d072/d072000',
                       controller: 'd072',
                       controllerAs: 'ctrl',
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
                   .state('OrgIns_Rpt', {
                       url: "/OrgIns_Rpt",
                       templateUrl: "/d070/Orang_InshRpt",
                       controller: "d070",
                       controllerAs: "ctrl",
                       params: {
                           UserID: '',
                           UserName: '',
                       }
                   })
            
           }]);
})();

