﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace Inus_AppsMang
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            // Stock Requests Routes
            routes.MapRoute(
                name: "StockRequests",
                url: "StockRequests/{action}/{id}",
                defaults: new { controller = "StockRequests", action = "Index", id = UrlParameter.Optional }
            );

            // Management Routes for a72
            routes.MapRoute(
                name: "RequestsManagement",
                url: "RequestsManagement/{action}/{id}",
                defaults: new { controller = "a72", action = "a72000", id = UrlParameter.Optional }
            );

            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "a27", action = "a26000", id = UrlParameter.Optional }
            );
        }
    }
}
