﻿using Inus_AppsMang;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Inus_AppsMang.Models
{
    public class AgPerCl
    {
        public float Value { get; set;}
        public int IsPercentge { get; set;}
       
    }
    public class DelSN_InsTypes
    {
        public int ID { get; set;}
        public string Des { get; set;}
        public List<DelDocs_Status> DocsSn { get; set; }
    }
    public class DelDocs_Status
    {
        public string SN { get; set; }
        public int Status { get; set; }
        public string StatusDesc { get; set; }
    }
    public class AdoptCL
    {
        public Guid ID { get; set; }
        public string Name { get; set; }
        public int Mont { get; set; }
        public int Year { get; set; }
        public int CompUnUsedCota { get; set; }
        public int TravUnUsedCota { get; set; }
        public int MidResUnUsedCota { get; set; }
        public int NumOfDocs_Orange { get; set; }
        public int NumOfDocs_Trav { get; set; }
        public int NumOfDocs_Comp { get; set; }
        public int NumOfDocs_Midres { get; set; }
        public int CompDeletedCota { get; set; }
        public int MidcResDeletedCota { get; set; }
        public int OrangResDeletedCota { get; set; }
        public int TravResDeletedCota { get; set; }
        public float CompProft { get; set; }
        public float MonthTot { get; set; }
        public float Left { get; set; }
        public float OprangeProft { get; set; }
        public float TarvProft { get; set; }
        public float MideResProf { get; set; }
        public float OrangAgenProftVal { get; set; }
        public float Comp_InsValue { get; set; }
        public float Comp_Tax_insu { get; set; }
        public float Comp_Tax_stamp { get; set; }
        public float Comp_Tax_supervision { get; set; }
        public float Comp_Tax { get; set; }
        public float CompTotals { get; set; }
        public float OraTotals { get; set; }
        public float TravTotals { get; set; }
        public float MidResTotals { get; set; }
        public bool Isdopt { get; set; }


    }
    public class GetSisID
    {
        Guid CompSysID = Guid.Parse("12196B48-95C5-4D51-A13D-091FD6D73E37");
        Guid OprangeSysID = Guid.Parse("7F8703F3-60AC-47C6-828D-29F8EA1B6246");
        Guid TarvSysID = Guid.Parse("F43D1CDD-BC69-42C9-9E73-D8249DDD4ADE");
        Guid MideResSysID = Guid.Parse("85A70D0B-C1CC-4B8E-8191-DBFB6ADE02A8");
        public Guid GetCompSysID()
        {
            return CompSysID;
        }
        public Guid GetTarvSysID()
        {
            return TarvSysID;
        }
        public Guid GetMideResSysID()
        {
            return MideResSysID;
        }
        public Guid GetOrangeSysID()
        {
            return OprangeSysID;
        }


    }
    public class Doc_Print
    {
        public float Tax { get; set; }
        public int DocType_ID { get; set; }
        public string DocType_Desc { get; set; }
        public string Com_Name { get; set; }
        public float Tax_supervision { get; set; }
        public float Tax_stamp { get; set; }
        public float Tax_insu { get; set; }
        public float Paper_No { get; set; }
        public float NomOfPassengers { get; set; }
        public string agencyCityAddres { get; set; }
        public string Chassis_number { get; set; }
        public string PhoneNum { get; set; }
        public string Address { get; set; }
        public string Agency { get; set; }
        public int Manufacturing_Year { get; set; }
        public int PayLoad { get; set; }
        public float Engine_Capacity { get; set; }
        public string Engine_number { get; set; }
        public string contry { get; set; }
        public string Card_NO { get; set; }
        public bool IsDelProg { get; set; }
        public string c_name { get; set; }
        public string f_date { get; set; }
        public string to_date { get; set; }
        public string color { get; set; }
        public int dur { get; set; }
        public string iron_bord { get; set; }
        public string durtype { get; set; }
        public string Ins_SN { get; set; }
        public string mas_name { get; set; }
        public string car_name { get; set; }
        public float ins_val { get; set; }
        public float TaxVal { get; set; }
        public byte status { get; set; }
        public float total_tax { get; set; }
        public string ValInletters { get; set; }
        public string InsuAdress { get; set; }
        public string OldDoc { get; set; }
        public string InsertBy { get; set; }
        public string InsertDate { get; set; }


        public Doc_Print print(CompulsoryInsurenceTB obj)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            Doc_Print doc = new Doc_Print();
            var Old = db.OtherServHis.Where(a => a.NewDocID == obj.Insu_ID && obj.DocType == a.Type).
                  Select(a => new { a.OldDoc, a.CompulsoryInsurenceTB1.Ins_SN }).ToList();
            doc.DocType_ID = obj.DocType;
            doc.DocType_Desc = obj.DocTypes == null ? db.DocTypes.Find(obj.DocType).DocTypeDesc : obj.DocTypes.DocTypeDesc;
            doc.Address = obj.Address;
            doc.Tax = (float?)obj.Tax ?? 0;
            doc.Tax_supervision = (float?)obj.Tax_supervision ?? 0;
            doc.Tax_stamp = (float?)obj.Tax_stamp ?? 0;
            doc.Tax_insu = (float?)obj.Tax_insu ?? 0;
            doc.Paper_No = obj.Paper_No;
            doc.NomOfPassengers = obj.NomOfPassengers;
            doc.agencyCityAddres = obj.Agency.Cities.CityName;
            doc.InsuAdress = obj.Cities == null ? db.Cities.Find(obj.WorckPlaceInsuID).CityName : obj.Cities.CityName;
            doc.Chassis_number = obj.Chassis_number;
            doc.PhoneNum = obj.PhoneNum;
            doc.Address = obj.Address;
            doc.Agency = obj.Agency.AgencyName;
            doc.Manufacturing_Year = obj.Manufacturing_Year;
            doc.PayLoad = (int)obj.PayLoad;
            doc.Engine_Capacity = (int)obj.Engine_Capacity;
            doc.Engine_number = obj.Engine_number;
            doc.contry = obj.Country == null ? db.Country.Find(obj.Manfactire_ContryID).CountryName : obj.Country.CountryName;
            doc.Ins_SN = obj.Ins_SN;
            doc.c_name = obj.CusName;
            doc.f_date = obj.DateFrom.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.to_date = obj.DateTo.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.color = obj.Colors == null ? db.Colors.Find(obj.ColorID).ColorNaame : obj.Colors.ColorNaame;
            doc.dur = (int)obj.Durations;
            doc.iron_bord = obj.IronBoard_num;
            doc.mas_name = obj.CompulsoryPriceMaster == null ? db.CompulsoryPriceMaster.Find(obj.CompMasterID).CompMasterName :
            obj.CompulsoryPriceMaster.CompMasterName;
            doc.car_name = obj.CarsBrand == null ? db.CarsBrand.Find(obj.CarBarndID).CarNaame : obj.CarsBrand.CarNaame;
            doc.durtype = obj.InsTypePerDurID == 1 ? "m" : "d";
            doc.ins_val = (float?)obj.InsValue ?? 0;
            doc.TaxVal = (float?)obj.Tax ?? 0;
            doc.total_tax = (float?)obj.TotalWithTax ?? 0;
            doc.ValInletters = new ToWord(Convert.ToDecimal(obj.TotalWithTax), new CurrencyInfo(CurrencyInfo.Currencies.Libya)).ConvertToArabic();
            doc.InsertBy = db.AgUsers.Find(obj.InsertBy).AgUserName;
            doc.InsertDate = obj.InsertDate.ToUniversalTime().AddHours(2).ToString("yyyy-MM-ddTHH:mm:ss");
            doc.OldDoc = Old.Count() == 0 ? " - " : Old[0].Ins_SN;
            return doc;
        }
        public Doc_Print printOR(Orange_Insurance_Policy obj)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            Doc_Print doc = new Doc_Print();
            //var OrangDocs_int1 = db.Orange_Insurance_Policy.Where(c => c.Status == 1 && c.IsDelProg == true);
            //doc.Address = obj.Insurance_Location;
            doc.Tax = (float?)obj.Insurance_Tax ?? 0;
            doc.Tax_supervision = (float?)obj.Insurance_Supervision ?? 0;
            doc.Tax_stamp = (float?)obj.Insurance_Stamp ?? 0;
            doc.Tax_insu = (float?)obj.Insurance_Version ?? 0;
            doc.Card_NO = obj.sr_Card_No;
            doc.agencyCityAddres = obj.Agency.Cities.CityName;
            doc.InsuAdress = obj.Orang_Country == null ? db.Orang_Country.Find(obj.Country_Symbol).CountryName : obj.Orang_Country.CountryName;
            doc.Chassis_number = obj.Chassis_Number;
            doc.PhoneNum = obj.Insurance_Phone;
            doc.Address = obj.Insurance_Location;
            doc.Agency = obj.Agency.AgencyName;
            doc.Manufacturing_Year = (int)obj.Car_Made_Date;
            doc.Engine_number = obj.motor_number;
            doc.c_name = obj.Insurance_Name;
            doc.f_date = obj.insurance_day_from.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.to_date = obj.insurance_day_to.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.dur = (int)obj.DurInDays;
            doc.iron_bord = obj.Plate_Number;
            doc.contry = "ليبية";
            doc.mas_name = obj.Orange_Insurance_Clause == null ? db.Orange_Insurance_Clause.Find(obj.Visited_Country_ID).Slug
                : obj.Orange_Insurance_Clause.Slug;
            doc.color = "-";
            doc.car_name = obj.Car_Symbol == null ? db.OrangeCars.Find(obj.Car_Symbol).carName : obj.OrangeCars.carName;
            doc.ins_val = (float?)obj.Insurance_Installment ?? 0;
            doc.TaxVal = (float?)obj.TaxTotal ?? 0;
            doc.total_tax = (float?)obj.Insurance_Total ?? 0;
            doc.ValInletters = new ToWord(Convert.ToDecimal(obj.Insurance_Total), new CurrencyInfo(CurrencyInfo.Currencies.Libya)).ConvertToArabic();
            doc.InsertBy = db.AgUsers.Find(obj.InsertBy).AgUserName;
            doc.InsertDate = obj.Insurance_Date.Value.ToUniversalTime().AddHours(2).ToString("yyyy-MM-ddTHH:mm:ss");
            return doc;
        }



        public Doc_Print printOldDoc(CompulsoryInsurenceTB obj)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            Doc_Print doc = new Doc_Print();
            var Old = db.oldOwner.Where(a => a.newDocId == obj.Insu_ID && obj.DocType == 4).
                  Select(a => new { a.oldDocNum, a.CompulsoryInsurenceTB.Ins_SN }).ToList();
            doc.DocType_ID = obj.DocType;
            doc.DocType_Desc = obj.DocTypes == null ? db.DocTypes.Find(obj.DocType).DocTypeDesc : obj.DocTypes.DocTypeDesc;
            doc.Address = obj.Address;
            doc.Tax = (float?)obj.Tax ?? 0;
            doc.Tax_supervision = (float?)obj.Tax_supervision ?? 0;
            doc.Tax_stamp = (float?)obj.Tax_stamp ?? 0;
            doc.Tax_insu = (float?)obj.Tax_insu ?? 0;
            doc.Paper_No = obj.Paper_No;
            doc.NomOfPassengers = obj.NomOfPassengers;
            doc.agencyCityAddres = obj.Agency.Cities.CityName;
            doc.InsuAdress = obj.Cities == null ? db.Cities.Find(obj.WorckPlaceInsuID).CityName : obj.Cities.CityName;
            doc.Chassis_number = obj.Chassis_number;
            doc.PhoneNum = obj.PhoneNum;
            doc.Address = obj.Address;
            doc.Agency = obj.Agency.AgencyName;
            doc.Manufacturing_Year = obj.Manufacturing_Year;
            doc.PayLoad = (int)obj.PayLoad;
            doc.Engine_Capacity = (int)obj.Engine_Capacity;
            doc.Engine_number = obj.Engine_number;
            doc.contry = obj.Country == null ? db.Country.Find(obj.Manfactire_ContryID).CountryName : obj.Country.CountryName;
            doc.Ins_SN = obj.Ins_SN;
            doc.c_name = obj.CusName;
            doc.f_date = obj.DateFrom.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.to_date = obj.DateTo.ToString("yyyy-MM-ddTHH:mm:ss");
            doc.color = obj.Colors == null ? db.Colors.Find(obj.ColorID).ColorNaame : obj.Colors.ColorNaame;
            doc.dur = (int)obj.Durations;
            doc.iron_bord = obj.IronBoard_num;
            doc.mas_name = obj.CompulsoryPriceMaster == null ? db.CompulsoryPriceMaster.Find(obj.CompMasterID).CompMasterName :
            obj.CompulsoryPriceMaster.CompMasterName;
            doc.car_name = obj.CarsBrand == null ? db.CarsBrand.Find(obj.CarBarndID).CarNaame : obj.CarsBrand.CarNaame;
            doc.durtype = obj.InsTypePerDurID == 1 ? "m" : "d";
            doc.ins_val = (float?)obj.InsValue ?? 0;
            doc.TaxVal = (float?)obj.Tax ?? 0;
            doc.total_tax = (float?)obj.TotalWithTax ?? 0;
            doc.ValInletters = new ToWord(Convert.ToDecimal(obj.TotalWithTax), new CurrencyInfo(CurrencyInfo.Currencies.Libya)).ConvertToArabic();
            doc.InsertBy = db.AgUsers.Find(obj.InsertBy).AgUserName;
            doc.InsertDate = obj.InsertDate.ToUniversalTime().AddHours(2).ToString("yyyy-MM-ddTHH:mm:ss");
            doc.OldDoc = Old.Count() == 0 ? " - " : Old[0].oldDocNum;
            return doc;
        }


    }
    class ToWord
    {
        /// Group Levels: 987,654,321.234
        /// 234 : Group Level -1
        /// 321 : Group Level 0
        /// 654 : Group Level 1
        /// 987 : Group Level 2

        #region Varaibles & Properties

        /// <summary>
        /// integer part
        /// </summary>
        private long _intergerValue;

        /// <summary>
        /// Decimal Part
        /// </summary>
        private int _decimalValue;

        /// <summary>
        /// Number to be converted
        /// </summary>
        public Decimal Number { get; set; }

        /// <summary>
        /// Currency to use
        /// </summary>
        public CurrencyInfo Currency { get; set; }

        /// <summary>
        /// English text to be placed before the generated text
        /// </summary>
        public String EnglishPrefixText { get; set; }

        /// <summary>
        /// English text to be placed after the generated text
        /// </summary>
        public String EnglishSuffixText { get; set; }

        /// <summary>
        /// Arabic text to be placed before the generated text
        /// </summary>
        public String ArabicPrefixText { get; set; }

        /// <summary>
        /// Arabic text to be placed after the generated text
        /// </summary>
        public String ArabicSuffixText { get; set; }
        #endregion

        #region General

        /// <summary>
        /// Constructor: short version
        /// </summary>
        /// <param name="number">Number to be converted</param>
        /// <param name="currency">Currency to use</param>
        /// 

        public ToWord(Decimal number, CurrencyInfo currency)
        {
            InitializeClass(number, currency, String.Empty, "only.", "فقط", "لا غير");
        }



        /// <summary>
        /// Constructor: Full Version
        /// </summary>
        /// <param name="number">Number to be converted</param>
        /// <param name="currency">Currency to use</param>
        /// <param name="englishPrefixText">English text to be placed before the generated text</param>
        /// <param name="englishSuffixText">English text to be placed after the generated text</param>
        /// <param name="arabicPrefixText">Arabic text to be placed before the generated text</param>
        /// <param name="arabicSuffixText">Arabic text to be placed after the generated text</param>
        public ToWord(Decimal number, CurrencyInfo currency, String englishPrefixText, String englishSuffixText, String arabicPrefixText, String arabicSuffixText)
        {
            InitializeClass(number, currency, englishPrefixText, englishSuffixText, arabicPrefixText, arabicSuffixText);
        }

        /// <summary>
        /// Initialize Class Varaibles
        /// </summary>
        /// <param name="number">Number to be converted</param>
        /// <param name="currency">Currency to use</param>
        /// <param name="englishPrefixText">English text to be placed before the generated text</param>
        /// <param name="englishSuffixText">English text to be placed after the generated text</param>
        /// <param name="arabicPrefixText">Arabic text to be placed before the generated text</param>
        /// <param name="arabicSuffixText">Arabic text to be placed after the generated text</param>
        private void InitializeClass(Decimal number, CurrencyInfo currency, String englishPrefixText, String englishSuffixText, String arabicPrefixText, String arabicSuffixText)
        {
            Number = number;
            Currency = currency;
            EnglishPrefixText = englishPrefixText;
            EnglishSuffixText = englishSuffixText;
            ArabicPrefixText = arabicPrefixText;
            ArabicSuffixText = arabicSuffixText;

            ExtractIntegerAndDecimalParts();
        }

        /// <summary>
        /// Get Proper Decimal Value
        /// </summary>
        /// <param name="decimalPart">Decimal Part as a String</param>
        /// <returns></returns>
        private string GetDecimalValue(string decimalPart)
        {
            string result = String.Empty;

            if (Currency.PartPrecision != decimalPart.Length)
            {
                int decimalPartLength = decimalPart.Length;

                for (int i = 0; i < Currency.PartPrecision - decimalPartLength; i++)
                {
                    decimalPart += "0"; //Fix for 1 number after decimal ( 10.5 , 1442.2 , 375.4 ) 
                }

                result = String.Format("{0}.{1}", decimalPart.Substring(0, Currency.PartPrecision), decimalPart.Substring(Currency.PartPrecision, decimalPart.Length - Currency.PartPrecision));

                result = (Math.Round(Convert.ToDecimal(result))).ToString();
            }
            else
                result = decimalPart;

            for (int i = 0; i < Currency.PartPrecision - result.Length; i++)
            {
                result += "0";
            }

            return result;
        }

        /// <summary>
        /// Eextract Interger and Decimal parts
        /// </summary>
        private void ExtractIntegerAndDecimalParts()
        {
            String[] splits = Number.ToString().Split('.');

            _intergerValue = Convert.ToInt32(splits[0]);

            if (splits.Length > 1)
                _decimalValue = Convert.ToInt32(GetDecimalValue(splits[1]));
        }
        #endregion

        #region English Number To Word

        #region Varaibles

        private static string[] englishOnes =
           new string[] {
            "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine",
            "Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"
        };

        private static string[] englishTens =
            new string[] {
            "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"
        };

        private static string[] englishGroup =
            new string[] {
            "Hundred", "Thousand", "Million", "Billion", "Trillion", "Quadrillion", "Quintillion", "Sextillian",
            "Septillion", "Octillion", "Nonillion", "Decillion", "Undecillion", "Duodecillion", "Tredecillion",
            "Quattuordecillion", "Quindecillion", "Sexdecillion", "Septendecillion", "Octodecillion", "Novemdecillion",
            "Vigintillion", "Unvigintillion", "Duovigintillion", "10^72", "10^75", "10^78", "10^81", "10^84", "10^87",
            "Vigintinonillion", "10^93", "10^96", "Duotrigintillion", "Trestrigintillion"
        };
        #endregion

        /// <summary>
        /// Process a group of 3 digits
        /// </summary>
        /// <param name="groupNumber">The group number to process</param>
        /// <returns></returns>
        private string ProcessGroup(int groupNumber)
        {
            int tens = groupNumber % 100;

            int hundreds = groupNumber / 100;

            string retVal = String.Empty;

            if (hundreds > 0)
            {
                retVal = String.Format("{0} {1}", englishOnes[hundreds], englishGroup[0]);
            }
            if (tens > 0)
            {
                if (tens < 20)
                {
                    retVal += ((retVal != String.Empty) ? " " : String.Empty) + englishOnes[tens];
                }
                else
                {
                    int ones = tens % 10;

                    tens = (tens / 10) - 2; // 20's offset

                    retVal += ((retVal != String.Empty) ? " " : String.Empty) + englishTens[tens];

                    if (ones > 0)
                    {
                        retVal += ((retVal != String.Empty) ? " " : String.Empty) + englishOnes[ones];
                    }
                }
            }

            return retVal;
        }

        /// <summary>
        /// Convert stored number to words using selected currency
        /// </summary>
        /// <returns></returns>
        public string ConvertToEnglish()
        {
            Decimal tempNumber = Number;

            if (tempNumber == 0)
                return "Zero";

            string decimalString = ProcessGroup(_decimalValue);

            string retVal = String.Empty;

            int group = 0;

            if (tempNumber < 1)
            {
                retVal = englishOnes[0];
            }
            else
            {
                while (tempNumber >= 1)
                {
                    int numberToProcess = (int)(tempNumber % 1000);

                    tempNumber = tempNumber / 1000;

                    string groupDescription = ProcessGroup(numberToProcess);

                    if (groupDescription != String.Empty)
                    {
                        if (group > 0)
                        {
                            retVal = String.Format("{0} {1}", englishGroup[group], retVal);
                        }

                        retVal = String.Format("{0} {1}", groupDescription, retVal);
                    }

                    group++;
                }
            }

            String formattedNumber = String.Empty;
            formattedNumber += (EnglishPrefixText != String.Empty) ? String.Format("{0} ", EnglishPrefixText) : String.Empty;
            formattedNumber += (retVal != String.Empty) ? retVal : String.Empty;
            //formattedNumber += (retVal != String.Empty) ? (_intergerValue == 1 ? Currency.EnglishCurrencyName : Currency.EnglishPluralCurrencyName) : String.Empty;
            formattedNumber += (decimalString != String.Empty) ? " and " : String.Empty;
            formattedNumber += (decimalString != String.Empty) ? decimalString : String.Empty;
            //formattedNumber += (decimalString != String.Empty) ? " " + (_decimalValue == 1 ? Currency.EnglishCurrencyPartName : Currency.EnglishPluralCurrencyPartName) : String.Empty;
            //formattedNumber += (EnglishSuffixText != String.Empty) ? String.Format(" {0}", EnglishSuffixText) : String.Empty;

            return formattedNumber;
        }

        public string CovertToArabicNumbers()
        {
            Decimal tempNumber = Number;
            string StrArb = tempNumber.ToString();


            StrArb = StrArb.ToString().Replace("0", "٠");
            StrArb = StrArb.ToString().Replace("1", "١");
            StrArb = StrArb.ToString().Replace("2", "٢");
            StrArb = StrArb.ToString().Replace("3", "٣");
            StrArb = StrArb.ToString().Replace("4", "٤");
            StrArb = StrArb.ToString().Replace("5", "٥");
            StrArb = StrArb.ToString().Replace("6", "٦");
            StrArb = StrArb.ToString().Replace("7", "٧");
            StrArb = StrArb.ToString().Replace("8", "٨");
            StrArb = StrArb.ToString().Replace("9", "٩");

            return StrArb;

        }

        #endregion

        #region Arabic Number To Word

        #region Varaibles

        private static string[] arabicOnes =
           new string[] {
            String.Empty, "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة",
            "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
        };

        private static string[] arabicFeminineOnes =
           new string[] {
            String.Empty, "إحدى", "اثنتان", "ثلاث", "أربع", "خمس", "ست", "سبع", "ثمان", "تسع",
            "عشر", "إحدى عشرة", "اثنتا عشرة", "ثلاث عشرة", "أربع عشرة", "خمس عشرة", "ست عشرة", "سبع عشرة", "ثماني عشرة", "تسع عشرة"
        };

        private static string[] arabicTens =
            new string[] {
            "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"
        };

        private static string[] arabicHundreds =
            new string[] {
            "", "مائة", "مئتان", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة","تسعمائة"
        };

        private static string[] arabicAppendedTwos =
            new string[] {
            "مئتا", "ألفا", "مليونا", "مليارا", "تريليونا", "كوادريليونا", "كوينتليونا", "سكستيليونا"
        };

        private static string[] arabicTwos =
            new string[] {
            "مئتان", "ألفان", "مليونان", "ملياران", "تريليونان", "كوادريليونان", "كوينتليونان", "سكستيليونان"
        };

        private static string[] arabicGroup =
            new string[] {
            "مائة", "ألف", "مليون", "مليار", "تريليون", "كوادريليون", "كوينتليون", "سكستيليون"
        };

        private static string[] arabicAppendedGroup =
            new string[] {
            "", "ألفاً", "مليوناً", "ملياراً", "تريليوناً", "كوادريليوناً", "كوينتليوناً", "سكستيليوناً"
        };

        private static string[] arabicPluralGroups =
            new string[] {
            "", "آلاف", "ملايين", "مليارات", "تريليونات", "كوادريليونات", "كوينتليونات", "سكستيليونات"
        };
        #endregion

        /// <summary>
        /// Get Feminine Status of one digit
        /// </summary>
        /// <param name="digit">The Digit to check its Feminine status</param>
        /// <param name="groupLevel">Group Level</param>
        /// <returns></returns>
        private string GetDigitFeminineStatus(int digit, int groupLevel)
        {
            if (groupLevel == -1)
            { // if it is in the decimal part
                if (Currency.IsCurrencyPartNameFeminine)
                    return arabicFeminineOnes[digit]; // use feminine field
                else
                    return arabicOnes[digit];
            }
            else
                if (groupLevel == 0)
            {
                if (Currency.IsCurrencyNameFeminine)
                    return arabicFeminineOnes[digit];// use feminine field
                else
                    return arabicOnes[digit];
            }
            else
                return arabicOnes[digit];
        }

        /// <summary>
        /// Process a group of 3 digits
        /// </summary>
        /// <param name="groupNumber">The group number to process</param>
        /// <returns></returns>
        private string ProcessArabicGroup(int groupNumber, int groupLevel, Decimal remainingNumber)
        {
            int tens = groupNumber % 100;

            int hundreds = groupNumber / 100;

            string retVal = String.Empty;

            if (hundreds > 0)
            {
                if (tens == 0 && hundreds == 2) // حالة المضاف
                    retVal = String.Format("{0}", arabicAppendedTwos[0]);
                else //  الحالة العادية
                    retVal = String.Format("{0}", arabicHundreds[hundreds]);
            }

            if (tens > 0)
            {
                if (tens < 20)
                { // if we are processing under 20 numbers
                    if (tens == 2 && hundreds == 0 && groupLevel > 0)
                    { // This is special case for number 2 when it comes alone in the group
                        if (_intergerValue == 2000 || _intergerValue == 2000000 || _intergerValue == 2000000000 || _intergerValue == 2000000000000 || _intergerValue == 2000000000000000 || _intergerValue == 2000000000000000000)
                            retVal = String.Format("{0}", arabicAppendedTwos[groupLevel]); // في حالة الاضافة
                        else
                            retVal = String.Format("{0}", arabicTwos[groupLevel]);//  في حالة الافراد
                    }
                    else
                    { // General case
                        if (retVal != String.Empty)
                            retVal += " و ";

                        if (tens == 1 && groupLevel > 0 && hundreds == 0)
                            retVal += " ";
                        else
                            if ((tens == 1 || tens == 2) && (groupLevel == 0 || groupLevel == -1) && hundreds == 0 && remainingNumber == 0)
                            retVal += String.Empty; // Special case for 1 and 2 numbers like: ليرة سورية و ليرتان سوريتان
                        else
                            retVal += GetDigitFeminineStatus(tens, groupLevel);// Get Feminine status for this digit
                    }
                }
                else
                {
                    int ones = tens % 10;
                    tens = (tens / 10) - 2; // 20's offset

                    if (ones > 0)
                    {
                        if (retVal != String.Empty)
                            retVal += " و ";

                        // Get Feminine status for this digit
                        retVal += GetDigitFeminineStatus(ones, groupLevel);
                    }

                    if (retVal != String.Empty)
                        retVal += " و ";

                    // Get Tens text
                    retVal += arabicTens[tens];
                }
            }

            return retVal;
        }

        /// <summary>
        /// Convert stored number to words using selected currency
        /// </summary>
        /// <returns></returns>
        public string ConvertToArabic()
        {
            Decimal tempNumber = Number;

            if (tempNumber == 0)
                return "صفر";

            // Get Text for the decimal part
            string decimalString = ProcessArabicGroup(_decimalValue, -1, 0);

            string retVal = String.Empty;
            Byte group = 0;
            while (tempNumber >= 1)
            {
                // seperate number into groups
                int numberToProcess = (int)(tempNumber % 1000);

                tempNumber = tempNumber / 1000;

                // convert group into its text
                string groupDescription = ProcessArabicGroup(numberToProcess, group, Math.Floor(tempNumber));

                if (groupDescription != String.Empty)
                { // here we add the new converted group to the previous concatenated text
                    if (group > 0)
                    {
                        if (retVal != String.Empty)
                            retVal = String.Format("{0} {1}", "و", retVal);

                        if (numberToProcess != 2)
                        {
                            if (numberToProcess % 100 != 1)
                            {
                                if (numberToProcess >= 3 && numberToProcess <= 10) // for numbers between 3 and 9 we use plural name
                                    retVal = String.Format("{0} {1}", arabicPluralGroups[group], retVal);
                                else
                                {
                                    if (retVal != String.Empty) // use appending case
                                        retVal = String.Format("{0} {1}", arabicAppendedGroup[group], retVal);
                                    else
                                        retVal = String.Format("{0} {1}", arabicGroup[group], retVal); // use normal case
                                }
                            }
                            else
                            {
                                retVal = String.Format("{0} {1}", arabicGroup[group], retVal); // use normal case
                            }
                        }
                    }

                    retVal = String.Format("{0} {1}", groupDescription, retVal);
                }

                group++;
            }

            String formattedNumber = String.Empty;
            formattedNumber += (ArabicPrefixText != String.Empty) ? String.Format("{0} ", ArabicPrefixText) : String.Empty;
            formattedNumber += (retVal != String.Empty) ? retVal : String.Empty;
            if (_intergerValue != 0)
            { // here we add currency name depending on _intergerValue : 1 ,2 , 3--->10 , 11--->99
                int remaining100 = (int)(_intergerValue % 100);

                if (remaining100 == 0)
                    formattedNumber += Currency.Arabic1CurrencyName;
                else
                    if (remaining100 == 1)
                    formattedNumber += Currency.Arabic1CurrencyName;
                else
                        if (remaining100 == 2)
                {
                    if (_intergerValue == 2)
                        formattedNumber += Currency.Arabic2CurrencyName;
                    else
                        formattedNumber += Currency.Arabic1CurrencyName;
                }
                else
                            if (remaining100 >= 3 && remaining100 <= 10)
                    formattedNumber += Currency.Arabic310CurrencyName;
                else
                                if (remaining100 >= 11 && remaining100 <= 99)
                    formattedNumber += Currency.Arabic1199CurrencyName;
            }
            formattedNumber += (_decimalValue != 0) ? " و " : String.Empty;
            formattedNumber += (_decimalValue != 0) ? decimalString : String.Empty;
            if (_decimalValue != 0)
            { // here we add currency part name depending on _intergerValue : 1 ,2 , 3--->10 , 11--->99
                formattedNumber += " ";

                int remaining100 = (int)(_decimalValue % 100);

                if (remaining100 == 0)
                    formattedNumber += Currency.Arabic1CurrencyPartName;
                else
                    if (remaining100 == 1)
                    formattedNumber += Currency.Arabic1CurrencyPartName;
                else
                        if (remaining100 == 2)
                    formattedNumber += Currency.Arabic2CurrencyPartName;
                else
                            if (remaining100 >= 3 && remaining100 <= 10)
                    formattedNumber += Currency.Arabic310CurrencyPartName;
                else
                                if (remaining100 >= 11 && remaining100 <= 99)
                    formattedNumber += Currency.Arabic1199CurrencyPartName;
            }
            formattedNumber += (ArabicSuffixText != String.Empty) ? String.Format(" {0}", ArabicSuffixText) : String.Empty;
            ArabicSuffixText = "";

            formattedNumber += (ArabicSuffixText != String.Empty) ? String.Format(" {0}", ArabicSuffixText) : String.Empty;

            return formattedNumber;
        }
        #endregion
    }

    public class CurrencyInfo
    {
        public enum Currencies { UAE = 0, Syria, SaudiArabia, Libya, Gold };

        #region Constructors

        public CurrencyInfo(Currencies currency)
        {
            switch (currency)
            {
                case Currencies.UAE:
                    CurrencyID = 0;
                    CurrencyCode = "AED";
                    IsCurrencyNameFeminine = false;
                    EnglishCurrencyName = "UAE Dirham";
                    EnglishPluralCurrencyName = "UAE Dirhams";
                    EnglishCurrencyPartName = "Fils";
                    EnglishPluralCurrencyPartName = "Fils";
                    Arabic1CurrencyName = "درهم إماراتي";
                    Arabic2CurrencyName = "درهمان إماراتيان";
                    Arabic310CurrencyName = "دراهم إماراتية";
                    Arabic1199CurrencyName = "درهماً إماراتياً";
                    Arabic1CurrencyPartName = "فلس";
                    Arabic2CurrencyPartName = "فلسان";
                    Arabic310CurrencyPartName = "فلوس";
                    Arabic1199CurrencyPartName = "فلساً";
                    PartPrecision = 2;
                    IsCurrencyPartNameFeminine = false;
                    break;


                case Currencies.Syria:
                    CurrencyID = 1;
                    CurrencyCode = "SYP";
                    IsCurrencyNameFeminine = true;
                    EnglishCurrencyName = "Syrian Pound";
                    EnglishPluralCurrencyName = "Syrian Pounds";
                    EnglishCurrencyPartName = "Piaster";
                    EnglishPluralCurrencyPartName = "Piasteres";
                    Arabic1CurrencyName = "ليرة سورية";
                    Arabic2CurrencyName = "ليرتان سوريتان";
                    Arabic310CurrencyName = "ليرات سورية";
                    Arabic1199CurrencyName = "ليرة سورية";
                    Arabic1CurrencyPartName = "قرش";
                    Arabic2CurrencyPartName = "قرشان";
                    Arabic310CurrencyPartName = "قروش";
                    Arabic1199CurrencyPartName = "قرشاً";
                    PartPrecision = 2;
                    IsCurrencyPartNameFeminine = false;
                    break;

                case Currencies.SaudiArabia:
                    CurrencyID = 2;
                    CurrencyCode = "SAR";
                    IsCurrencyNameFeminine = false;
                    EnglishCurrencyName = "Saudi Riyal";
                    EnglishPluralCurrencyName = "Saudi Riyals";
                    EnglishCurrencyPartName = "Halala";
                    EnglishPluralCurrencyPartName = "Halalas";
                    Arabic1CurrencyName = "ريال سعودي";
                    Arabic2CurrencyName = "ريالان سعوديان";
                    Arabic310CurrencyName = "ريالات سعودية";
                    Arabic1199CurrencyName = "ريالاً سعودياً";
                    Arabic1CurrencyPartName = "هللة";
                    Arabic2CurrencyPartName = "هللتان";
                    Arabic310CurrencyPartName = "هللات";
                    Arabic1199CurrencyPartName = "هللة";
                    PartPrecision = 2;
                    IsCurrencyPartNameFeminine = true;
                    break;

                case Currencies.Libya:
                    CurrencyID = 3;
                    CurrencyCode = "LYD";
                    IsCurrencyNameFeminine = false;
                    EnglishCurrencyName = "Libyan Dinar";
                    EnglishPluralCurrencyName = "Libyan Dinars";
                    EnglishCurrencyPartName = "milim";
                    EnglishPluralCurrencyPartName = "millimes";
                    Arabic1CurrencyName = "دينار ";
                    Arabic2CurrencyName = "ديناران ";
                    Arabic310CurrencyName = "دنانير ";
                    Arabic1199CurrencyName = "ديناراً ";
                    Arabic1CurrencyPartName = "قرش";
                    Arabic2CurrencyPartName = "قرشان";
                    Arabic310CurrencyPartName = "قروش";
                    Arabic1199CurrencyPartName = "قرشا";
                    PartPrecision = 2;
                    IsCurrencyPartNameFeminine = false;
                    break;

                case Currencies.Gold:
                    CurrencyID = 4;
                    CurrencyCode = "XAU";
                    IsCurrencyNameFeminine = false;
                    EnglishCurrencyName = "Gram";
                    EnglishPluralCurrencyName = "Grams";
                    EnglishCurrencyPartName = "Milligram";
                    EnglishPluralCurrencyPartName = "Milligrams";
                    Arabic1CurrencyName = "جرام";
                    Arabic2CurrencyName = "جرامان";
                    Arabic310CurrencyName = "جرامات";
                    Arabic1199CurrencyName = "جراماً";
                    Arabic1CurrencyPartName = "ملجرام";
                    Arabic2CurrencyPartName = "ملجرامان";
                    Arabic310CurrencyPartName = "ملجرامات";
                    Arabic1199CurrencyPartName = "ملجراماً";
                    PartPrecision = 2;
                    IsCurrencyPartNameFeminine = false;
                    break;

            }
        }

        #endregion

        #region Properties

        /// <summary>
        /// Currency ID
        /// </summary>
        public int CurrencyID { get; set; }

        /// <summary>
        /// Standard Code
        /// Syrian Pound: SYP
        /// UAE Dirham: AED
        /// </summary>
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Is the currency name feminine ( Mua'anath مؤنث)
        /// ليرة سورية : مؤنث = true
        /// درهم : مذكر = false
        /// </summary>
        public Boolean IsCurrencyNameFeminine { get; set; }

        /// <summary>
        /// English Currency Name for single use
        /// Syrian Pound
        /// UAE Dirham
        /// </summary>
        public string EnglishCurrencyName { get; set; }

        /// <summary>
        /// English Plural Currency Name for Numbers over 1
        /// Syrian Pounds
        /// UAE Dirhams
        /// </summary>
        public string EnglishPluralCurrencyName { get; set; }

        /// <summary>
        /// Arabic Currency Name for 1 unit only
        /// ليرة سورية
        /// درهم إماراتي
        /// </summary>
        public string Arabic1CurrencyName { get; set; }

        /// <summary>
        /// Arabic Currency Name for 2 units only
        /// ليرتان سوريتان
        /// درهمان إماراتيان
        /// </summary>
        public string Arabic2CurrencyName { get; set; }

        /// <summary>
        /// Arabic Currency Name for 3 to 10 units
        /// خمس ليرات سورية
        /// خمسة دراهم إماراتية
        /// </summary>
        public string Arabic310CurrencyName { get; set; }

        /// <summary>
        /// Arabic Currency Name for 11 to 99 units
        /// خمس و سبعون ليرةً سوريةً
        /// خمسة و سبعون درهماً إماراتياً
        /// </summary>
        public string Arabic1199CurrencyName { get; set; }

        /// <summary>
        /// Decimal Part Precision
        /// for Syrian Pounds: 2 ( 1 SP = 100 parts)
        /// for Tunisian Dinars: 3 ( 1 TND = 1000 parts)
        /// </summary>
        public Byte PartPrecision { get; set; }

        /// <summary>
        /// Is the currency part name feminine ( Mua'anath مؤنث)
        /// هللة : مؤنث = true
        /// قرش : مذكر = false
        /// </summary>
        public Boolean IsCurrencyPartNameFeminine { get; set; }

        /// <summary>
        /// English Currency Part Name for single use
        /// Piaster
        /// Fils
        /// </summary>
        public string EnglishCurrencyPartName { get; set; }

        /// <summary>
        /// English Currency Part Name for Plural
        /// Piasters
        /// Fils
        /// </summary>
        public string EnglishPluralCurrencyPartName { get; set; }

        /// <summary>
        /// Arabic Currency Part Name for 1 unit only
        /// قرش
        /// هللة
        /// </summary>
        public string Arabic1CurrencyPartName { get; set; }

        /// <summary>
        /// Arabic Currency Part Name for 2 unit only
        /// قرشان
        /// هللتان
        /// </summary>
        public string Arabic2CurrencyPartName { get; set; }

        /// <summary>
        /// Arabic Currency Part Name for 3 to 10 units
        /// قروش
        /// هللات
        /// </summary>
        public string Arabic310CurrencyPartName { get; set; }

        /// <summary>
        /// Arabic Currency Part Name for 11 to 99 units
        /// قرشاً
        /// هللةً
        /// </summary>
        public string Arabic1199CurrencyPartName { get; set; }
        #endregion
    }

    public class SettingObj
    {
        public string sysName { get; set; }
        public Guid InsertBy { get; set; }
        public List<setting> Settings { get; set; }

    }

    public class MonthsCL
    {
        public string MOnthArName { get; set; }
        public int Month { get; set; }
        public DateTime Sdate { get; set; }
        public DateTime Edate { get; set; }


    }
    public class DurSettingList
    {
        public System.Guid ID { get; set; }
        public float Per { get; set; }
        public string Desc { get; set; }
        public int month { get; set; }
    }
    public class setting
    {
        public int ID { get; set; }
        public string Label { get; set; }
        public float Value { get; set; }
    }
    public class Trav_SettingsClass
    {
        public System.Guid DurationID { get; set; }
        public System.Guid AgeID { get; set; }
        public float Months3 { get; set; }
        public float DurationPsix { get; set; }
        public float DurationPNine { get; set; }
        public float DurationPTwelve { get; set; }
        public float DurationPFifteen { get; set; }
        public float DurationPEgihteen { get; set; }
        public float DurationPTwentyO { get; set; }
        public float DurationPTwentyF { get; set; }
        public float DurationPT1 { get; set; }
        public float DurationPT2 { get; set; }
        public float Years2 { get; set; }
        public float Years3 { get; set; }
        public float Years4 { get; set; }
        public float Years5 { get; set; }
        public float DocRepValue { get; set; }

        public float AgePZtoTwelve { get; set; }
        public float AgePTtoFifty { get; set; }
        public float AgePFtoSixty { get; set; }
        public float AgePStoSeventy { get; set; }
        public float AgePStoHundreed { get; set; }
        public float RepDocProftMargin { get; set; }

        public float Stamp { get; set; }
        public float Super { get; set; }
        public float Tax { get; set; }
        public float Issuance { get; set; }
        public float DocRepprofValue { get; set; }

        public DateTime AgeUpdateDate { get; set; }
        public DateTime DUpdateDate { get; set; }


    }

    public class AgPermClass
    {
        public bool Cump_Sys { get; set; }
        public bool Cump_ChangeOwnership { get; set; }
        public bool Cump_AdditionalServices { get; set; }
        public bool Cump_OldInterface { get; set; }
        public bool Orange_Sys { get; set; }
        public bool Orange_Inventory { get; set; }
        public bool Travel_Sys { get; set; }
        public bool Trav_Calculation { get; set; }
        public bool Trav_ShowAllDocs { get; set; }
        public bool Med_ResponsibilitySys { get; set; }
        public bool Un_countedper { get; set; }
        public bool print_normal { get; set; }
        public bool paper_A4 { get; set; }
        public bool colored_paper { get; set; }
        public bool inventory_requests { get; set; }
        public bool inventory_a_requests { get; set; }
        public Guid selecteAgencyID { get; set; }
        public Guid InsertBy { get; set; }
        public AgPermClass getPerbyAgencyID(Guid selecteAgencyID)
        {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            AgPermClass Agcl = new AgPermClass();
            var Agperm = db.AgencyPermission.Where(c => c.Status == 1 && c.AgencyID == selecteAgencyID);
            Agcl.Cump_Sys = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_Sys").Count() == 1;
            Agcl.Cump_ChangeOwnership = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_ChangeOwnership").Count() == 1;
            Agcl.Cump_AdditionalServices = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_AdditionalServices").Count() == 1;
            Agcl.Cump_OldInterface = Agperm.Where(c => c.AgPermissionsList.EngName == "Cump_OldInterface").Count() == 1;
            Agcl.Orange_Sys = Agperm.Where(c => c.AgPermissionsList.EngName == "Orange_Sys").Count() == 1;
            Agcl.Orange_Inventory = Agperm.Where(c => c.AgPermissionsList.EngName == "Orange_Inventory").Count() == 1;
            Agcl.Travel_Sys = Agperm.Where(c => c.AgPermissionsList.EngName == "Travel_Sys").Count() == 1;
            Agcl.Trav_Calculation = Agperm.Where(c => c.AgPermissionsList.EngName == "Trav_Calculation").Count() == 1;
            Agcl.Trav_ShowAllDocs = Agperm.Where(c => c.AgPermissionsList.EngName == "Trav_ShowAllDocs").Count() == 1;
            Agcl.Med_ResponsibilitySys = Agperm.Where(c => c.AgPermissionsList.EngName == "Med_ResponsibilitySys").Count() == 1;
            Agcl.Un_countedper = Agperm.Where(c => c.AgPermissionsList.EngName == "Un_countedper").Count() == 1;
            Agcl.print_normal = Agperm.Where(c => c.AgPermissionsList.EngName == "print_normal").Count() == 1;
            Agcl.paper_A4 = Agperm.Where(c => c.AgPermissionsList.EngName == "paper_A4").Count() == 1;
            Agcl.colored_paper = Agperm.Where(c => c.AgPermissionsList.EngName == "colored_paper").Count() == 1;
            Agcl.inventory_requests = Agperm.Where(c => c.AgPermissionsList.EngName == "inventory_requests").Count() == 1;
            Agcl.inventory_a_requests = Agperm.Where(c => c.AgPermissionsList.EngName == "inventory_a_requests").Count() == 1;
            return Agcl;
        }

    }

    public class DelApprovels_Cl
    {
        public Guid DocID { get; set; }
        public string docnumb { get; set; }
        public string DocTypeName { get; set; }
        public string name { get; set; }
        public string Sn { get; set; }
        public string Color { get; set; }
        public string CarName { get; set; }
        public string Sdate { get; set; }
        public string DateEnd { get; set; }
        public int Dur { get; set; }
        public int DocTypeID { get; set; }
        public string Col_name { get; set; }
        public string intime { get; set; }
        public string AgName { get; set; }
        public string FullName { get; set; }
        public float Val { get; set; }
        public float Total { get; set; }
        public Guid AgID { get; set; }
        public Guid InsertBy { get; set; }

    }
    public class d067Cl
    {
        public Nullable<Guid> MaritalStatusID { get; set; }
        public string MaritalStatus { get; set; }
        public Guid InsertedBy { get; set; }
    }
    public class d066Cl
    {
        public Nullable<Guid> ProfessionID { get; set; }
        public string ProfessionName { get; set; }
        public float Price { get; set; }
        public Guid InsertedBy { get; set; }
    }
    public class d065Cl
    {
        public Nullable<Guid> NationalityID { get; set; }
        public string NationalityName { get; set; }
        public Guid InsertedBy { get; set; }
    }
    public class d064Cl
    {
        public Nullable<Guid> AddressID { get; set; }
        public string AddressName { get; set; }
        public Guid InsertedBy { get; set; }
    }
    public class perCalsss
    {
        public bool users { get; set; }
        public bool agency { get; set; }
        public bool comp_contries { get; set; }
        public bool comp_Color { get; set; }
        public bool settings { get; set; }
        public bool inventory_a_requests { get; set; }
        public bool news { get; set; }
        public bool palance { get; set; }
        public bool comp_seralies { get; set; }
        public bool agincu_contractEnd { get; set; }
        public bool reports { get; set; }
        public bool comp_deleteAprovels { get; set; }
        public bool comp_inshDurations { get; set; }
        public bool comp_carTypes { get; set; }
        public bool comp_compaines { get; set; }
        public bool traviles_Sys { get; set; }
        public bool trav_contries { get; set; }
        public bool trav_ranges { get; set; }
        public bool trav_Seariles { get; set; }
        public bool trav_reports { get; set; }
        public bool comp_Mangments { get; set; }
        public bool compolsy_Sys { get; set; }
        public bool orang_sys { get; set; }
        public bool Orange_Contries { get; set; }
        public bool Orang_cars { get; set; }
        public bool comp_reports { get; set; }
        public bool orang_reports { get; set; }
        public bool trav_deletedPapers { get; set; }
        public bool medical_sys { get; set; }
        public bool med_adress { get; set; }
        public bool med_nat { get; set; }
        public bool med_job { get; set; }
        public bool marital_stat { get; set; }
        public bool med_percentage { get; set; }
        public bool agency_sys { get; set; }
        public bool finance_sys { get; set; }
        public bool agency_deleteAprovels { get; set; }
        public bool InsuranceInterfacemanagmentSys { get; set; }
        public Guid selectedUserID { get; set; }
        public Guid InsertBy { get; set; }
        public bool med_academic { get; set; }
        public bool MainAcc { get; set; }
        public bool Orang_Seariles { get; set; }

        public perCalsss getPerbyUserID(Guid selectedUserID)
        {
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            perCalsss cl = new perCalsss();
            var perm = db.UserPermissions.Where(c => c.Status == 1 && c.UserId == selectedUserID);
            cl.users = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.agency = perm.Where(c => c.PermissionsList.EngName == "agency").Count() == 1;
            cl.comp_contries = perm.Where(c => c.PermissionsList.EngName == "comp_contries").Count() == 1;
            cl.comp_Color = perm.Where(c => c.PermissionsList.EngName == "comp_Color").Count() == 1;
            cl.settings = perm.Where(c => c.PermissionsList.EngName == "settings").Count() == 1;
            cl.news = perm.Where(c => c.PermissionsList.EngName == "news").Count() == 1;
            cl.palance = perm.Where(c => c.PermissionsList.EngName == "palance").Count() == 1;
            cl.comp_seralies = perm.Where(c => c.PermissionsList.EngName == "comp_seralies").Count() == 1;
            cl.reports = perm.Where(c => c.PermissionsList.EngName == "reports").Count() == 1;
            cl.comp_deleteAprovels = perm.Where(c => c.PermissionsList.EngName == "comp_deleteAprovels").Count() == 1;
            cl.comp_inshDurations = perm.Where(c => c.PermissionsList.EngName == "comp_inshDurations").Count() == 1;
            cl.comp_carTypes = perm.Where(c => c.PermissionsList.EngName == "comp_carTypes").Count() == 1;
            cl.comp_compaines = perm.Where(c => c.PermissionsList.EngName == "comp_compaines").Count() == 1;
            cl.traviles_Sys = perm.Where(c => c.PermissionsList.EngName == "traviles_Sys").Count() == 1;
            cl.trav_contries = perm.Where(c => c.PermissionsList.EngName == "trav_contries").Count() == 1;
            cl.trav_ranges = perm.Where(c => c.PermissionsList.EngName == "trav_ranges").Count() == 1;
            cl.trav_Seariles = perm.Where(c => c.PermissionsList.EngName == "trav_Seariles").Count() == 1;
            cl.trav_reports = perm.Where(c => c.PermissionsList.EngName == "trav_reports").Count() == 1;
            cl.comp_Mangments = perm.Where(c => c.PermissionsList.EngName == "comp_Mangments").Count() == 1;
            cl.compolsy_Sys = perm.Where(c => c.PermissionsList.EngName == "compolsy_Sys").Count() == 1;
            cl.orang_sys = perm.Where(c => c.PermissionsList.EngName == "orang_sys").Count() == 1;
            cl.Orange_Contries = perm.Where(c => c.PermissionsList.EngName == "Orange_Contries").Count() == 1;
            cl.Orang_cars = perm.Where(c => c.PermissionsList.EngName == "Orang_cars").Count() == 1;
            cl.comp_reports = perm.Where(c => c.PermissionsList.EngName == "comp_reports").Count() == 1;
            cl.orang_reports = perm.Where(c => c.PermissionsList.EngName == "orang_reports").Count() == 1;
            cl.trav_deletedPapers = perm.Where(c => c.PermissionsList.EngName == "trav_deletedPapers").Count() == 1;
            cl.medical_sys = perm.Where(c => c.PermissionsList.EngName == "medical_sys").Count() == 1;
            cl.med_adress = perm.Where(c => c.PermissionsList.EngName == "med_adress").Count() == 1;
            cl.med_nat = perm.Where(c => c.PermissionsList.EngName == "med_nat").Count() == 1;
            cl.med_job = perm.Where(c => c.PermissionsList.EngName == "med_job").Count() == 1;
            cl.marital_stat = perm.Where(c => c.PermissionsList.EngName == "marital_stat").Count() == 1;
            cl.med_percentage = perm.Where(c => c.PermissionsList.ID == 111).Count() == 1;
            cl.agency_sys = perm.Where(c => c.PermissionsList.EngName == "agency_sys").Count() == 1;
            cl.finance_sys = perm.Where(c => c.PermissionsList.EngName == "finance_sys").Count() == 1;
            cl.agency_deleteAprovels = perm.Where(c => c.PermissionsList.EngName == "agency_deleteAprovels").Count() == 1;
            cl.InsuranceInterfacemanagmentSys = perm.Where(c => c.PermissionsList.EngName == "InsuranceInterfacemanagmentSys").Count() == 1;
            cl.Orang_Seariles = perm.Where(c => c.PermissionsList.EngName == "Orang_Seariles").Count() == 1;
            cl.med_percentage = perm.Where(c => c.PermissionsList.EngName == "med_percentage").Count() == 1;
            cl.med_academic = perm.Where(c => c.PermissionsList.EngName == "med_academic").Count() == 1;
                            cl.MainAcc = perm.Where(c => c.PermissionsList.EngName == "MainAcc").Count() == 1;
            return cl;
        }
    }
    public class OrangCount
    {
        public int ID { get; set; }
        public string CountName { get; set; }

    }
    public class OrangCl
    {

        public string GetApi()
        {
            return "https://prodapi.lifo.ly";
        }
    }
    public class AgLoginCl
    {
        public string UserName { get; set; }
        public string Password { get; set; }
    }
    public class Trav_zoonConCL
    {

        public Guid? ID { get; set; }
        public Guid ContID { get; set; }
        public Guid ZonnID { get; set; }
        public Guid InsertBy { get; set; }
    }
    public class a51Class
    {

        public Guid AgencyID { get; set; }
        public string AgencyName { get; set; }
        public double totTax { get; set; }
        public double Tax { get; set; }
        public double Tax_insu { get; set; }
        public double Tax_stamp { get; set; }
        public double TotalWithTax { get; set; }
        public double TaxSupervision { get; set; }
        public double Ins_Value { get; set; }
        public int docCount { get; set; }
        public string AgNum { get; set; }


    }




    public class res_agenCL
    {

        public Guid? ID { get; set; }
        public Guid AgencyID { get; set; }
        public Guid UserID { get; set; }
        public Guid InsertBy { get; set; }
        public Guid UpdateBy { get; set; }
        public bool ISdeleted { get; set; }
        public DateTime InsertDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }
    public class a50Cl
    {
        public Guid? ID { get; set; }
        public string Name { get; set; }
        public bool IsT { get; set; }
        public bool IsStam { get; set; }
        public float SpfesVa { get; set; }
        public bool IsIns { get; set; }
        public bool IsSub { get; set; }
        public Guid InsertBy { get; set; }
    }

    public class a46BCl
    {
        public Nullable<Guid> Det_ID { get; set; }
        public string Deta_Name { get; set; }
        public Guid Mas_ID { get; set; }
        public Guid InsertBy { get; set; }
        public float Price { get; set; }
        public int MaxPass { get; set; }
        public int MinPass { get; set; }
        public float C_loader { get; set; }
        public float MinCapIng { get; set; }
        public float MaxCapIng { get; set; }
        public float pas_price { get; set; }
        public int PassCount { get; set; }
    }
    public class perCl
    {
        public bool users { get; set; }
        public bool agency { get; set; }
        public bool contries { get; set; }
        public bool carColors { get; set; }
        public bool settings { get; set; }
        public bool News { get; set; }
        public bool AgencyPlanse { get; set; }
        public bool SearilsStock { get; set; }
        public bool Reports { get; set; }
        public bool AproveDocDeletion { get; set; }
        public bool AProveRenewDeletion { get; set; }
        public bool AProveReplaceDeletion { get; set; }
        public bool insurncePairod { get; set; }
        public bool Cars { get; set; }
        public bool Companies { get; set; }
        public bool Countriestraveler { get; set; }
        public bool Zoontraveler { get; set; }
        public bool Stooktraveler { get; set; }
        public bool Rpttraveler { get; set; }

        public perCl GetPermByUser(Guid UserID)
        {
            // GET: a46
            Insh_AppsDBEntities db = new Insh_AppsDBEntities();
            perCl cl = new perCl();
            var perm = db.UserPermissions.Where(c => c.Status == 1 && c.UserId == UserID);
            cl.users = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.carColors = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.agency = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.contries = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.carColors = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.settings = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.News = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.AgencyPlanse = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.SearilsStock = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.Reports = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.AproveDocDeletion = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.AProveRenewDeletion = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.AProveReplaceDeletion = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.Cars = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.insurncePairod = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.Companies = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.Countriestraveler = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.Zoontraveler = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.Stooktraveler = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            cl.Rpttraveler = perm.Where(c => c.PermissionsList.EngName == "users").Count() == 1;
            return cl;
        }
    }
    public class a45Cl
    {
        public Guid? Car_ID { get; set; }
        public string Car_Name { get; set; }
        public string Car_NameEN { get; set; }
        public Guid InsertBy { get; set; }
    }


    public class a20Cl
    {
        public Nullable<Guid> col_ID { get; set; }
        public string col_Name { get; set; }
        public string col_NameEN { get; set; }
        public Guid InsertBy { get; set; }
    }

    public class a45ChartCalss
    {
        public Guid MinistryofLaborID { get; set; }
        public string Name { get; set; }
        public string JobName { get; set; }
        public string NameEN { get; set; }
        public string CountryName { get; set; }
        public string JobNameEN { get; set; }
        public string CountryNameEN { get; set; }
        public float tot { get; set; }
        public DateTime InsertDate { get; set; }


    }


    public class RptDatesList
    {
        public string TextDate { get; set; }
        public string Dateform { get; set; }
        public string DateTo { get; set; }

        public int MyProperty { get; set; }

    }



    public class AgUserClass
    {
        public Guid AgUId { get; set; }
        public string AgUName { get; set; }
        public string FName { get; set; }
        public string Pass { get; set; }
        public Guid? UserID { get; set; }
        public Guid? AgencyID { get; set; }
    }
    public class a40Class
    {
        public System.Guid ID { get; set; }
        public string Name { get; set; }
        public string NameEN { get; set; }
        public bool ReplaceCheck { get; set; }
        public bool RenewCheck { get; set; }
        public bool AdSearchCheck { get; set; }
        public bool CanClassDocs { get; set; }
        public System.DateTime Del_Date { get; set; }
        public double Dibt { get; set; }
        public float ProfitMargin { get; set; }
        public bool CanExtendCheak { get; set; }
        public int RptType { get; set; }
        //public float Exchange { get; set; }
        public System.Guid SID { get; set; }
        public System.Guid CatID { get; set; }
        public int IsADibt { get; set; }
        public double debtVal { get; set; }
        public string PhoneNum { get; set; }
        public Nullable<byte> Status { get; set; }
        public string MID { get; set; }
        public Nullable<System.DateTime> InsertDate { get; set; }
        public string MID2 { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public System.Guid FID { get; set; }
        public Nullable<bool> IS { get; set; }
        public System.Guid AID { get; set; }
        public System.Guid AgUserID { get; set; }
        public string AgFullName { get; set; }
        public string Aname { get; set; }
        public string image { get; set; }
        public string Apass { get; set; }
        public byte AgencyType { get; set; }
        public byte IsLocalCurrnecy { get; set; }
        public int ContryCoID { get; set; }
        public bool? HasUser { get; set; }
        public Nullable<bool> Trav_IsADibt { get; set; }
        public Nullable<double> Trav_debtVal { get; set; }
        public Nullable<double> Trav_Palance { get; set; }
        public Nullable<double> Trav_SoldValue { get; set; }
        public Nullable<double> Trav_AddPalance { get; set; }


    }


    public class AgeSettingList
    {
        public System.Guid ID { get; set; }
        public float Per { get; set; }
        public string Desc { get; set; }
    }


    public class reportesClass
    {
        public DateTime Datefrom { get; set; }
        public DateTime DateTo { get; set; }

        public int years { get; set; }
        public int months { get; set; }

        public int? selectedYear { get; set; }
        public int? selectedMonth { get; set; }
        public bool AllAgency { get; set; }

        public bool AllUser { get; set; }

        public System.Guid AgencyID { get; set; }

        public System.Guid AgUserID { get; set; }
        public int selectredio { get; set; }
        public int RadioBut { get; set; }

        public string[] GID { get; set; }
        //public string[] GuID { get; set; }
        public bool AllAgencyViewCHk { get; set; }


        public List<string> AlwaysFilled { get; set; }


    }
    public class PrintClass
    {

        public int SeStart { get; set; }
        public int SeEnd { get; set; }
        public int NumOFSer { get; set; }
        public int PrinterID { get; set; }
        public Guid UserID { get; set; }

    }
    public class ID_Name
    {

        public Guid ID { get; set; }
        public string Name { get; set; }

    }

    public class BalanceClass
    {
        public string ID2 { get; set; }

        public int BalanceID { get; set; }
        public System.Guid CID { get; set; }
        public int BalanceType { get; set; }
        public float AddBalance { get; set; }
        public string BalanceNotes { get; set; }
        public float Value { get; set; }
        public string ValueNotes { get; set; }
        public float SoldeBalance { get; set; }
        public bool IsAllowedDibt { get; set; }
        public DateTime InsertDate { get; set; }
        public string InsertedBy { get; set; }
        public int Exch { get; set; }
        public int Status { get; set; }

    }
    public class TVBalanceClass
    {
        public int BalanceID { get; set; }
        public Nullable<int> BalanceType { get; set; }
        public Nullable<double> Value { get; set; }
        public string ValueNotes { get; set; }
        public Nullable<System.DateTime> InsertDate { get; set; }
        public string InsertedBy { get; set; }
        public System.Guid AgencyID { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public string UpdatedBy { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<double> ExChangeLi { get; set; }

    }

    public class NewsClass
    {
        public System.Guid NewsID { get; set; }
        public System.Guid AgencyID { get; set; }

        public string NewsText { get; set; }
        public int Status { get; set; }
        public string Title { get; set; }
        public string InsertBy { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }


    }

    public class CitiesClass
    {
        public string CitID { get; set; }
        public string CitName { get; set; }
        public string CitNameEN { get; set; }
        public string CntryID { get; set; }

        public int Status { get; set; }

        public string InsertBy { get; set; }
    }
    public class CountryClass
    {
        public string CntryID { get; set; }
        public string CntryName { get; set; }
        public string CntryNameEN { get; set; }
        public string InsertBy { get; set; }
    }







    public class Trav_CitiesClass
    {
        public string CitID { get; set; }
        public string CitName { get; set; }
        public string CitNameEN { get; set; }
        public string CntryID { get; set; }

        public int Status { get; set; }

        public string InsertBy { get; set; }
    }
    public class ZoneClass
    {
        public System.Guid ZID { get; set; }
        public string ZName { get; set; }
        public string InsertBy { get; set; }
        public string text { get; set; }
        public string textEN { get; set; }
        public float Pes { get; set; }
        public byte? ZStatus { get; set; }

    }
    public class Trav_CountryClass
    {

        public Guid CntryID { get; set; }
        public string CntryName { get; set; }

        public string CntryNameEN { get; set; }
        public string CurncyName { get; set; }
        public string CurncyNameEn { get; set; }
        public string CurncyArPrfex { get; set; }
        public string CurncyENPrfex { get; set; }
        public float TourismInsPrice { get; set; }
        public int postCode { get; set; }
        public float HartIssuePer { get; set; }
        public float ExchangeValue { get; set; }
        public float earIeIssuePer { get; set; }
        public float WorkInsPrice { get; set; }
        public float BodyIssuePer { get; set; }

        public Guid zID { get; set; }

        public int Status { get; set; }

        public string InsertBy { get; set; }
    }



    public class SerrClass
    {
        public Guid SID { get; set; }
        public Int64 SeStart { get; set; }
        public Int64 SeEnd { get; set; }
        public byte AdType { get; set; }
        public byte State { get; set; }
        public int NumOFSer { get; set; }
        public Guid? Agnt_ID { get; set; }
        public byte Status { get; set; }
        public Guid InsertBy { get; set; }
        public DateTime Insert_Date { get; set; }

    }
    public class UserClass
    {
        public string UsID { get; set; }
        public string UsName { get; set; }
        public string FullName { get; set; }
        public string Password { get; set; }
        public string PhoneNum { get; set; }
        public int Status { get; set; }
        public int UsType { get; set; }
        public int UsTypeID { get; set; }
        public string InsertBy { get; set; }
    }





    public class Orang_rptCl
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public string GID { get; set; }
        public string AllAgencyViewCHk { get; set; }
        public string GuID { get; set; }
        public bool ISchecked { get; set; }
        public string thisOptionValue { get; set; }
        public int selectedYear { get; set; }
        public string selectedMonth { get; set; }
    }

    public class Li2Cl
    {
        public Guid AgencyID { get; set; }
        public string AgencyName { get; set; }
    }

    public class agency_Or_li
    {
        public Guid ID { get; set; }
        public string Name { get; set; }
    }

    public class Insur_class
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();

        public Guid GetLibyaID()
        {
            return Guid.Parse("86A60DFB-DCE1-4916-993D-A3394CD23EA6");
        }
        public bool HasPer(string UserID, int PerID)
        {
            Guid xx = Guid.Parse(UserID);
            if (UserID == "")
                return false;
            return db.UserPermissions.Any(p => p.PermissionId == PerID && p.UserId == xx && p.Status == 1);
        }

        public bool IsExsit(string UserID)
        {
            Guid xx = Guid.Parse(UserID);
            if (UserID == "")
                return false;
            var us = db.Users.Where(p => p.UserID == xx && p.Status == 1).ToList();
            if (us.Count() == 1)
                return true;
            else
                return false;


        }



    }

    public class AgencyEquipmentClass
    {
        public Guid? ID { get; set; }
        public Guid AgencyID { get; set; }
        public int CategoryID { get; set; }
        public string ItemName { get; set; }
        public string Brand { get; set; }
        public string Model { get; set; }
        public string SerialNumber { get; set; }
        public int Quantity { get; set; }
        public decimal? Price { get; set; }
        public byte Status { get; set; }
        public string Notes { get; set; }
        public Guid InsertedBy { get; set; }
        public DateTime? InsertedDate { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        
        // Additional properties for display
        public string CategoryName { get; set; }
        public string AgencyName { get; set; }
        public string StatusText { get; set; }
    }

    public class EquipmentCategoryClass
    {
        public int CategoryID { get; set; }
        public string CategoryName { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
    }
}