﻿using Inus_AppsMang.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Runtime.Remoting.Metadata.W3cXsd2001;
using System.Web.Mvc;

namespace Inus_AppsMang.Controllers
{
    public class a40Controller : Controller
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();

        public ActionResult a40000()
        {
            return View("AgencyPage");
        }


        public ActionResult a400072(Guid AgencyID, Guid UserID)
        {

            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                AgPermClass cl = new AgPermClass();
                var AgPer = cl.getPerbyAgencyID(AgencyID);


                return Json(new { ErrorCode = 0, AgPer });
            }
            catch (Exception ex)

            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجى الاتصال بفريق الدعم الفني " });
            }
        }

        public ActionResult a400071(AgPermClass AgPerObj)
        {
            try
            {

                Insur_class ic = new Insur_class();
                if (!ic.HasPer(AgPerObj.InsertBy.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var Ag_per = db.AgencyPermission.Where(c => c.AgencyID == AgPerObj.selecteAgencyID).ToList();

                var Agper1 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Cump_Sys").ToList();
                if (Agper1.Count() == 0)

                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.AgencyID =  AgPerObj.selecteAgencyID;
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Cump_Sys").ID;
                    Agobj.Status = (byte)(AgPerObj.Cump_Sys ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper1.Count() > 0)
                {

                    AgencyPermission Agobj = Agper1[0];
                    Agobj.Status = (byte)(AgPerObj.Cump_Sys ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper2 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Cump_ChangeOwnership").ToList();
                if (Agper2.Count() == 0)

                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Cump_ChangeOwnership").ID;
                    Agobj.Status = (byte)(AgPerObj.Cump_ChangeOwnership ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper2.Count() > 0)
                {

                    AgencyPermission Agobj = Agper1[0];
                    Agobj.Status = (byte)(AgPerObj.Cump_ChangeOwnership ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper3 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Cump_AdditionalServices").ToList();
                if (Agper3.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Cump_AdditionalServices").ID;
                    Agobj.Status = (byte)(AgPerObj.Cump_AdditionalServices ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper3.Count() > 0)
                {
                    AgencyPermission Agobj = Agper3[0];
                    Agobj.Status = (byte)(AgPerObj.Cump_AdditionalServices ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper4 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Cump_OldInterface").ToList();
                if (Agper4.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Cump_OldInterface").ID;
                    Agobj.Status = (byte)(AgPerObj.Cump_OldInterface ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper4.Count() > 0)
                {
                    AgencyPermission Agobj = Agper4[0];
                    Agobj.Status = (byte)(AgPerObj.Cump_OldInterface ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper5 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Orange_Sys").ToList();
                if (Agper5.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Orange_Sys").ID;
                    Agobj.Status = (byte)(AgPerObj.Orange_Sys ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper5.Count() > 0)
                {
                    AgencyPermission Agobj = Agper5[0];
                    Agobj.Status = (byte)(AgPerObj.Orange_Sys ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper5_1 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Orange_Inventory").ToList();
                if (Agper5_1.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Orange_Inventory").ID;
                    Agobj.Status = (byte)(AgPerObj.Orange_Inventory ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper5_1.Count() > 0)
                {
                    AgencyPermission Agobj = Agper5_1[0];
                    Agobj.Status = (byte)(AgPerObj.Orange_Inventory ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper6 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Travel_Sys").ToList();
                if (Agper6.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Travel_Sys").ID;
                    Agobj.Status = (byte)(AgPerObj.Travel_Sys ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper6.Count() > 0)
                {
                    AgencyPermission Agobj = Agper6[0];
                    Agobj.Status = (byte)(AgPerObj.Travel_Sys ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper7 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Trav_Calculation").ToList();
                if (Agper7.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Trav_Calculation").ID;
                    Agobj.Status = (byte)(AgPerObj.Trav_Calculation ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper7.Count() > 0)
                {
                    AgencyPermission Agobj = Agper7[0];
                    Agobj.Status = (byte)(AgPerObj.Trav_Calculation ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper8 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Trav_ShowAllDocs").ToList();
                if (Agper8.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Trav_ShowAllDocs").ID;
                    Agobj.Status = (byte)(AgPerObj.Trav_ShowAllDocs ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper8.Count() > 0)
                {
                    AgencyPermission Agobj = Agper8[0];
                    Agobj.Status = (byte)(AgPerObj.Trav_ShowAllDocs ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                } 
                var Agper9 = Ag_per.Where(c => c.AgPermissionsList.EngName == "medical_sys").ToList();
                if (Agper9.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "medical_sys").ID;
                    Agobj.Status = (byte)(AgPerObj.Med_ResponsibilitySys ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper9.Count() > 0)
                {
                    AgencyPermission Agobj = Agper9[0];
                    Agobj.Status = (byte)(AgPerObj.Med_ResponsibilitySys ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper10 = Ag_per.Where(c => c.AgPermissionsList.EngName == "Un_countedper").ToList();
                if (Agper10.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "Un_countedper").ID;
                    Agobj.Status = (byte)(AgPerObj.Un_countedper ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper10.Count() > 0)
                {
                    AgencyPermission Agobj = Agper10[0];
                    Agobj.Status = (byte)(AgPerObj.Un_countedper ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper11 = Ag_per.Where(c => c.AgPermissionsList.EngName == "print_normal").ToList();
                if (Agper11.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "print_normal").ID;
                    Agobj.Status = (byte)(AgPerObj.print_normal ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper11.Count() > 0)
                {
                    AgencyPermission Agobj = Agper11[0];
                    Agobj.Status = (byte)(AgPerObj.print_normal ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper12 = Ag_per.Where(c => c.AgPermissionsList.EngName == "paper_A4").ToList();
                if (Agper12.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "paper_A4").ID;
                    Agobj.Status = (byte)(AgPerObj.paper_A4 ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper12.Count() > 0)
                {
                    AgencyPermission Agobj = Agper12[0];
                    Agobj.Status = (byte)(AgPerObj.paper_A4 ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var Agper13 = Ag_per.Where(c => c.AgPermissionsList.EngName == "colored_paper").ToList();
                if (Agper13.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "colored_paper").ID;
                    Agobj.Status = (byte)(AgPerObj.colored_paper ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper13.Count() > 0)
                {
                    AgencyPermission Agobj = Agper13[0];
                    Agobj.Status = (byte)(AgPerObj.colored_paper ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                //var Agper14 = Ag_per.Where(c => c.AgPermissionsList.EngName == "medical_sys").ToList();
                //if (Agper14.Count() == 0)
                //{
                //    AgencyPermission Agobj = new AgencyPermission();
                //    Agobj.ID = Guid.NewGuid();
                //    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                //    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "medical_sys").ID;
                //    Agobj.Status = (byte)(AgPerObj.Med_ResponsibilitySys ? 1 : 3);
                //    Agobj.InsertedDate = DateTime.Now;
                //    Agobj.InsertedBy = AgPerObj.InsertBy;
                //    db.AgencyPermission.Add(Agobj);
                //    db.SaveChanges();
                //}
                //if (Agper14.Count() > 0)
                //{
                //    AgencyPermission Agobj = Agper14[0];
                //    Agobj.Status = (byte)(AgPerObj.Med_ResponsibilitySys ? 1 : 3);
                //    Agobj.UpdatedDate = DateTime.Now;
                //    Agobj.UpdatedBy = AgPerObj.InsertBy;
                //    db.AgencyPermission.Attach(Agobj);
                //    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                //    db.SaveChanges();
                //}
                var Agper15 = Ag_per.Where(c => c.AgPermissionsList.EngName == "inventory_requests").ToList();
                if (Agper15.Count() == 0)
                {
                    AgencyPermission Agobj = new AgencyPermission();
                    Agobj.ID = Guid.NewGuid();
                    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "inventory_requests").ID;
                    Agobj.Status = (byte)(AgPerObj.inventory_requests ? 1 : 3);
                    Agobj.InsertedDate = DateTime.Now;
                    Agobj.InsertedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Add(Agobj);
                    db.SaveChanges();
                }
                if (Agper15.Count() > 0)
                {
                    AgencyPermission Agobj = Agper15[0];
                    Agobj.Status = (byte)(AgPerObj.inventory_requests ? 1 : 3);
                    Agobj.UpdatedDate = DateTime.Now;
                    Agobj.UpdatedBy = AgPerObj.InsertBy;
                    db.AgencyPermission.Attach(Agobj);
                    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                //var Agper16 = Ag_per.Where(c => c.AgPermissionsList.EngName == "inventory_a_requests").ToList();
                //if (Agper16.Count() == 0)
                //{
                //    AgencyPermission Agobj = new AgencyPermission();
                //    Agobj.ID = Guid.NewGuid();
                //    Agobj.AgencyID = AgPerObj.selecteAgencyID;
                //    Agobj.AgPermissionID = db.AgPermissionsList.SingleOrDefault(c => c.EngName == "inventory_a_requests").ID;
                //    Agobj.Status = (byte)(AgPerObj.inventory_a_requests ? 1 : 3);
                //    Agobj.InsertedDate = DateTime.Now;
                //    Agobj.InsertedBy = AgPerObj.InsertBy;
                //    db.AgencyPermission.Add(Agobj);
                //    db.SaveChanges();
                //}
                //if (Agper16.Count() > 0)
                //{
                //    AgencyPermission Agobj = Agper15[0];
                //    Agobj.Status = (byte)(AgPerObj.inventory_a_requests ? 1 : 3);
                //    Agobj.UpdatedDate = DateTime.Now;
                //    Agobj.UpdatedBy = AgPerObj.InsertBy;
                //    db.AgencyPermission.Attach(Agobj);
                //    db.Entry(Agobj).State = System.Data.Entity.EntityState.Modified;
                //    db.SaveChanges();
                //}

                return Json(new { ErrorCode = 0 });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجى الاتصال بفريق الدعم الفني " });
            }
        }
       
        public ActionResult a400034(Guid AgUId, byte Status, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                Comp_Agency ag = db.Comp_Agency.Find(AgUId);
                ag.Status = Status;
                ag.updateDate = DateTime.Now.ToUniversalTime().AddHours(2);
                ag.UpdateBY = UserID;
                db.SaveChanges();
                return Json(new { ErrorCode = 0, });

            }
            catch (Exception)
            {

                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });

            }

        }
        /// insert
        public ActionResult a400033(Guid AgencyID, Guid Comp_ID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                Comp_Agency ag = new Comp_Agency();
                ag.CoAG_ID = Guid.NewGuid();
                ag.CompID = Comp_ID;
                ag.Status = 1;
                ag.AgencyID = AgencyID;
                ag.InsertBy = UserID;
                ag.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                db.Comp_Agency.Add(ag);
                db.SaveChanges();
                return Json(new { ErrorCode = 0, });

            }
            catch (Exception)
            {

                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });

            }

        }
        // get list 
        public ActionResult a400032(Guid ID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var CompLi = db.Cus_Companies.Where(c => c.Status != 3 && c.Comp_Agency.Where(a => a.AgencyID == ID && a.Status != 3).Count() == 0).Select(c => new
                {
                    ID = c.Comp_ID,
                    Name = c.CompName,
                }).ToList();

                return Json(new { ErrorCode = 0, CompLi });

            }
            catch (Exception)
            {

                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });

            }

        }

        /// get  table
        public ActionResult a400031(Guid AgencyID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var obj = db.Comp_Agency.Where(c => c.Status != 3 && c.AgencyID == AgencyID).Select(c => new
                {
                    ID = c.CoAG_ID,
                    compName = c.Cus_Companies.CompName,
                    coId = c.CompID,
                    c.Status,
                    c.InsertDate,
                    c.InsertBy
                }).ToList();
                var agCompTb = obj.Select(c => new
                {
                    c.ID,
                    c.coId,
                    c.compName,
                    c.Status,
                    InsertDate = c.InsertDate.ToString("yyyy-MM-dd hh:mm tt"),
                    InsertBy = db.Users.Find(c.InsertBy).FullName,
                }).ToList();
                return Json(new { ErrorCode = 0, agCompTb });

            }
            catch (Exception)
            {

                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });

            }

        }
        public ActionResult a400030(Guid ID, int Status, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                AgUsers agUsers = db.AgUsers.Find(ID);
                agUsers.Status = (byte)Status;
                agUsers.UpdateBy = UserID.ToString();
                db.AgUsers.Attach(agUsers);
                db.Entry(agUsers).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception)
            {

                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });

            }

        }
        public ActionResult a4000027(AgUserClass obj)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(obj.UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                if (db.AgUsers.Where(c => c.AgUserName == obj.AgUName && c.AgUserID != obj.AgUId && c.Status == 1).Any())
                    return Json(new { ErrorCode = 3 });
                AgUsers agUsers = db.AgUsers.Find(obj.AgUId);
                agUsers.AgUserName = obj.AgUName;
                agUsers.FullName = obj.FName;
                agUsers.UpdateDate = DateTime.Now.ToUniversalTime().AddHours(2);
                agUsers.Passowrd = obj.Pass;
                agUsers.UpdateBy = obj.UserID.ToString();
                db.AgUsers.Attach(agUsers);
                db.Entry(agUsers).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0 });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a4000026(string UserName, Guid UserID, Guid? AgUId)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                bool IsExitUs;
                if (AgUId == null)
                    IsExitUs = db.AgUsers.Where(c => c.AgUserName == UserName && c.Status == 1).Any();
                else
                    IsExitUs = db.AgUsers.Where(c => c.AgUserName == UserName && c.AgUserID != AgUId && c.Status == 1).Any();
                return Json(new { ErrorCode = 0, IsExitUs });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a4000025(AgUserClass obj)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(obj.UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                if (db.AgUsers.Where(c => c.AgUserName == obj.AgUName && c.Status == 1).Any())
                    return Json(new { ErrorCode = 3 });
                AgUsers agUsers = new AgUsers();
                agUsers.AgUserID = Guid.NewGuid();
                agUsers.AgUserName = obj.AgUName;
                agUsers.FullName = obj.FName;
                agUsers.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                agUsers.Passowrd = obj.Pass;
                agUsers.InsertBy = obj.UserID.ToString();
                agUsers.Status = 1;
                agUsers.IsAdmin = false;
                agUsers.ISReportUser = true;
                agUsers.AgentId = obj.AgencyID;
                db.AgUsers.Add(agUsers);
                db.SaveChanges();
                return Json(new { ErrorCode = 0 });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a400021(Guid ID, Guid UserID)
        {
            try
            {


                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                var obj = db.Cus_Companies.Where(c => c.Status != 3).Select(c => new
                {
                    ID = c.Comp_ID,
                    Name = c.CompName,
                    c.InsertDate,
                    c.InsertBy,
                }).ToList();
                var AgentUsers = obj.Select(c => new
                {
                    c.ID,
                    c.Name,
                    InsDate = c.InsertDate.ToString("yyyy-MM-dd HH:mm tt"),
                    InsBy = db.Users.Find(c.InsertBy).FullName
                }).ToList();
                return Json(new { ErrorCode = 0, AgentUsers });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40012(Guid ID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                var Img = db.Agency.Find(ID).AgImg;

                return Json(new { ErrorCode = 0, Img });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40011(Guid ID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                var Img = db.Agency.Find(ID).AgImg;

                return Json(new { ErrorCode = 0, Img });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40010(Guid UserID, int AgencyType)
        {
            try
            {


                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                var AgenCats = db.AgenCat.Where(c => c.Status == 1 && AgencyType == c.AgenTypeID).Select(p => new
                {
                    ID = p.AgenCatID,
                    Name = p.AgenCatDesc,
                }).ToList();

                return Json(new { ErrorCode = 0, AgenCats });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40009(a40Class obj, byte Status, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var StopA = db.Agency.FirstOrDefault(p => p.AgencyID == obj.ID);
                StopA.Status = Status;
                StopA.UpdateDate = DateTime.Now.ToUniversalTime().AddHours(2);
                StopA.UpdatedBy = UserID.ToString();
                db.SaveChanges();
                if (Status == 1)
                {
                    return Json(new { ErrorCode = 1 });
                }
                else if (Status == 2)
                {
                    return Json(new { ErrorCode = 2 });
                }
                else
                    return Json(new { ErrorCode = 0 });


            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }

        public ActionResult a40007(a40Class obj, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var Exist = db.Agency.AsEnumerable().Where(x => x.AgencyName == obj.Name).Any();
                if (Exist) return Json(new { ErrorCode = 0 });
                else
                    return Json(new { ErrorCode = 2 });

            }
            catch (Exception)
            {

                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });

            }

        }

        public ActionResult a40006(a40Class obj, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var DelA = db.Agency.FirstOrDefault(p => p.AgencyID == obj.ID);
                DelA.Status = 3;
                DelA.UpdatedBy = UserID.ToString();
                DelA.UpdateDate = DateTime.Now;
                db.Agency.Attach(DelA);
                db.Entry(DelA).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40005(a40Class obj,List<AgPerCl> AgProObj, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var IsExistUs = db.AgUsers.Where(c => c.AgUserName == obj.Aname).ToList(); ;
                if (IsExistUs.Where(c => c.AgUserID != obj.AgUserID).Any())
                {
                    return Json(new { ErrorCode = 5 });
                }
               

                var EditA = db.Agency.Find(obj.ID);
                EditA.AgencyName = obj.Name;
                EditA.AgenNameEN = obj.NameEN;
                EditA.AgImg = obj.image;
                EditA.AgencyType = obj.AgencyType;
                EditA.AgenCatID = obj.CatID;
                EditA.AgNum = obj.ContryCoID;
                EditA.ContryID = obj.FID;
                EditA.ContryID = obj.FID;
                EditA.PhoneNum = obj.PhoneNum;
         
                EditA.IsAllowedDibt = obj.IsADibt == 0 ? false : true;
                EditA.AllowedDibtValue = obj.IsADibt == 1 ? Math.Round(obj.debtVal, 2) : 0;
                EditA.ProftMargin = obj.ProfitMargin;
                EditA.UpdateDate = obj.UpdateDate;
                EditA.UpdatedBy = UserID.ToString();
                EditA.CityID = obj.SID;
                //EditA.MidRes_IsADibt = obj.MidRes_IsADibt ;
                //EditA.Orang_IsADibt = obj.Orang_IsADibt;
                //EditA.Orang_AllowedDibtValue = obj.Orang_AllowedDibtValue;
                //EditA.MidRes_AllowedDibtValue = obj.MidRes_AllowedDibtValue;
                //EditA.Trav_IsAllowedDibt = obj.Trav_IsADibt;
                //EditA.Trav_AllowedDibtValue = obj.Trav_debtVal;
                //EditA.IntiaiPalance = obj.IntiaiPalance;
                //EditA.LemitDayesPaym = obj.LemitDayesPaym;
                db.SaveChanges();
                var EditAu = IsExistUs.SingleOrDefault(c => c.IsAdmin == true);
                EditAu.AgUserName = obj.Aname;
                EditAu.Passowrd = obj.Apass;
                EditAu.FullName = obj.AgFullName;
                EditAu.UpdateDate = DateTime.Now.ToUniversalTime().AddHours(2);
                EditAu.UpdateBy = UserID.ToString();
                db.SaveChanges();
                var Sys = db.Systems.Where(c => c.TyPe == 1).ToList();
                for(int i = 0; i < Sys.Count(); i++)
                {
                    var co = db.AgenSysProfit.Where(c => c.Status == 1 && c.SysID == Sys[i].Sys_ID && c.AgencyID == EditA.AgencyID).ToList();
                    if (co.Count() == 0)
                    {
                        AgenSysProfit objPro = new AgenSysProfit();
                        objPro.SysID = Guid.NewGuid();
                        objPro.SysID = Sys[i].Sys_ID;
                        objPro.AgencyID = EditA.AgencyID;
                        objPro.Status = 1;
                        objPro.Val = AgProObj[i].Value;
                        objPro.IsPercenge = AgProObj[i].IsPercentge == 1 ? true : false;
                        objPro.InsertDate = DateTime.Now;
                        objPro.InsertBy = UserID;
                        db.AgenSysProfit.Add(objPro);
                        db.SaveChanges();
                    }else if(co.Count() == 1)
                    {
                        AgenSysProfit objPro = co[0];
                        objPro.SysID = Sys[i].Sys_ID;
                        objPro.AgencyID = EditA.AgencyID;
                        objPro.Status = 1;
                        objPro.Val = AgProObj[i].Value;
                        objPro.IsPercenge = AgProObj[i].IsPercentge == 1 ? true : false;
                        objPro.InsertDate = DateTime.Now;
                        objPro.InsertBy = UserID;
                        db.AgenSysProfit.Attach(objPro);
                        db.Entry(objPro).State = System.Data.Entity.EntityState.Modified;
                        db.SaveChanges();
                    }
                }
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40004(string FID)
        {
            try
            {

                Guid CoID = Guid.Parse(FID);
                var CityFilter = db.Cities.Where(p => p.Status == 1 && p.CountryID == CoID).Select(p => new
                {
                    p.CityID,
                    p.CityName,

                }).ToList();
                return Json(new { ErrorCode = 0, CityFilter });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40003()
        {
            try
            {

                var CountryFilter = db.Country.Where(p => p.Status == 1).Select(p => new
                {
                    p.CountryID,
                    p.CountryName,

                }).ToList();
                return Json(new { ErrorCode = 0, CountryFilter });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }


        public ActionResult a40002(a40Class obj,List<AgPerCl> AgProObj, Guid UserID)
        {


            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                int IsExist = db.AgUsers.Where(c => c.AgUserName == obj.Aname).Count(); ;
                if (IsExist > 0)
                {
                    return Json(new { ErrorCode = 5 });
                }
                int IsExistNum = db.Agency.Where(c => c.AgNum == obj.ContryCoID).Count(); ;
                if (IsExist > 0)
                {
                    return Json(new { ErrorCode = 6 });
                }

                Agency mainObj = new Agency();
                mainObj.AgencyID = Guid.NewGuid();
                mainObj.AgencyName = obj.Name;
                mainObj.AgenNameEN = obj.NameEN;
                mainObj.AgencyType = obj.AgencyType;
                mainObj.Del_Date = DateTime.Now.ToUniversalTime().AddHours(2);
                mainObj.AgNum = obj.ContryCoID;
                mainObj.IsAllowedDibt = obj.IsADibt == 0 ? false : true;
                mainObj.AllowedDibtValue = obj.IsADibt == 1 ? Math.Round(obj.debtVal, 2) : 0;
                mainObj.ContryID = obj.FID;
                mainObj.AgenCatID = obj.CatID;
                mainObj.ProftMargin = obj.ProfitMargin;
                mainObj.AgImg = obj.image;
                mainObj.Status = 1;
                mainObj.PhoneNum = obj.PhoneNum;
               
                mainObj.InsertedBy = obj.MID;
                mainObj.CityID = obj.SID;
                mainObj.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                //mainObj.MidRes_IsADibt = obj.MidRes_IsADibt;
                //mainObj.Orang_IsADibt = obj.Orang_IsADibt;
                //mainObj.Orang_AllowedDibtValue = obj.Orang_AllowedDibtValue;
                //mainObj.MidRes_AllowedDibtValue = obj.MidRes_AllowedDibtValue;
                //mainObj.Trav_IsAllowedDibt = obj.Trav_IsADibt;
                //mainObj.Trav_AllowedDibtValue = obj.Trav_debtVal;
                //mainObj.IntiaiPalance = obj.IntiaiPalance;
                //mainObj.LemitDayesPaym = obj.LemitDayesPaym;
                mainObj.Trav_AddPalance = obj.Trav_AddPalance;
                mainObj.Trav_IsAllowedDibt = obj.Trav_IsADibt;
                mainObj.Trav_AllowedDibtValue = obj.Trav_debtVal;
                db.Agency.Add(mainObj);
                db.SaveChanges();
                AgUsers AgUsers = new AgUsers();
                AgUsers.AgUserID = Guid.NewGuid();
                AgUsers.AgUserName = obj.Aname;
                AgUsers.FullName = obj.AgFullName;
                AgUsers.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                AgUsers.Passowrd = obj.Apass;
                AgUsers.InsertBy = obj.MID;
                AgUsers.Status = 1;
                AgUsers.IsAdmin = true;
                AgUsers.AgentId = mainObj.AgencyID;
                db.AgUsers.Add(AgUsers);
                db.SaveChanges();
                Comp_Agency ag = new Comp_Agency();
                ag.CoAG_ID = Guid.NewGuid();
                ag.CompID = Guid.Parse("F42F6690-4E98-4F07-B700-74EA1487A4F7");
                ag.AgencyID = mainObj.AgencyID;
                ag.InsertBy = UserID;
                ag.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                db.Comp_Agency.Add(ag);
                db.SaveChanges();
                var Sys = db.Systems.Where(c => c.TyPe == 1).ToList();
                for (int i = 0; i < Sys.Count(); i++)
                {
                    AgenSysProfit objPro = new AgenSysProfit();
                    objPro.SysID = Guid.NewGuid();
                    objPro.SysID = Sys[i].Sys_ID;
                    objPro.AgencyID = mainObj.AgencyID;
                    objPro.Status = 1;
                    objPro.Val = AgProObj[i].Value;
                    objPro.IsPercenge = AgProObj[i].IsPercentge == 1 ? true : false;
                    objPro.InsertDate = DateTime.Now;
                    objPro.InsertBy = UserID;
                    db.AgenSysProfit.Add(objPro);
                    db.SaveChanges();
                }
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception )
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a40001(Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }
                var AgUser = db.AgUsers.Where(p => p.IsAdmin == true).ToList();

                var AgList = db.Agency.Where(p => p.Status != 3 && p.AgenCat.AgenTypeID != 3)
                    .Select(p => new
                    {
                        p.AgencyID,
                        p.AgencyName,
                        p.AgenNameEN,
                        p.AgencyType,
                        p.AgNum,
                        p.Del_Date,
                        p.ContryID,
                        p.AgenCatID,
                        p.AgenCat.AgenCatDesc,
                        p.CityID,
                        p.IsAllowedDibt,
                        p.MidRes_IsADibt,
                        p.Orang_IsADibt,
                        p.AllowedDibtValue,
                        p.PhoneNum,
                        p.ProftMargin,
                        p.Orang_AllowedDibtValue,
                        p.IsLocalCurrnecy,
                        p.Status,
                        p.InsertDate,
                        p.InsertedBy,
                        p.MidRes_AllowedDibtValue,
                        p.Cities.CityName,
                        p.Country.CountryName,
                        p.IntiaiPalance,
                        p.UnusedDocsLimt,
                        p.LemitDayesPaym,
                        Trav_debtVal = p.Trav_AllowedDibtValue,
                        Trav_IsADibt = p.Trav_IsAllowedDibt,
                        p.Trav_Palance,
                        p.Trav_SoldValue,
                        p.Trav_AddPalance,
                        MainAccID = p.AgenMainAccID,
                        MainAccName = p.AgenMainAcc.AccDesc ?? "-",
                        p.AgenMainAccID,

                    }).OrderBy(c => c.InsertDate).ToList();


                var AgencyList = AgList.Select(p => new
                {
                    p.AgencyID,
                    p.AgencyName,
                    NameEn = p.AgenNameEN == null ? "-" : p.AgenNameEN,
                    AgencyType = p.AgenCatDesc,
                    AgencyTypeID = p.AgenCatID,
                    agType = p.AgencyType,
                    Agnum = p.AgNum,
                    p.Del_Date,
                    p.ContryID,
                    p.AgenCatID,
                    p.AgenCatDesc,
                    p.CityID,
                    p.AgenMainAccID,
                    ProftMargin = p.ProftMargin ?? 0,
                    p.PhoneNum,
                    MidRes_IsADibt = p.MidRes_IsADibt ?? false,
                    MidRes_AllowedDibtValue = p.MidRes_AllowedDibtValue ?? 0,
                    Orang_AllowedDibtValue = p.Orang_AllowedDibtValue ?? 0,
                    Orang_IsADibt = p.Orang_IsADibt ?? false,
                    IntiaiPalance = p.IntiaiPalance ?? 0,
                    UnusedDocsLimt = p.UnusedDocsLimt ?? 0,
                    LemitDayesPaym = p.LemitDayesPaym ?? 0,
                    IsAllowedDibt = (p.IsAllowedDibt ?? false) == false ? 0 : 1,
                    AllowedDibtValue = p.AllowedDibtValue ?? 0,
                    IsLocalCurrnecy = p.IsLocalCurrnecy == true ? 1 : 0,
                    p.Status,
                    p.InsertDate,
                    p.CityName,
                    p.CountryName,
                    InsertedDate = p.InsertDate.Value.ToString("yyyy-MM-dd hh:mm tt"),
                    InsertedBy = p.InsertedBy == null ? "-" : db.Users.Find(Guid.Parse(p.InsertedBy)).FullName,
                    AgUserID = AgUser.SingleOrDefault(c => c.AgentId == p.AgencyID)?.AgUserID,
                    AgUserName = AgUser.SingleOrDefault(c => c.AgentId == p.AgencyID)?.AgUserName,
                    Passowrd = AgUser.SingleOrDefault(c => c.AgentId == p.AgencyID)?.Passowrd,
                    FullName = AgUser.SingleOrDefault(c => c.AgentId == p.AgencyID)?.FullName,
                    p.Trav_debtVal,
                    Trav_IsADibt=p.Trav_IsADibt,
                    p.Trav_Palance,
                    p.Trav_SoldValue,
                    p.Trav_AddPalance,
                    MainAccID = p.MainAccID,
                    MainAccName = p.MainAccName ?? "-",


                }).ToList();
                var li = ic.GetLibyaID();
                return Json(new { ErrorCode = 0, AgencyList, li });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        
        // دالة جلب قائمة التصنيفات الرئيسية
        public ActionResult a40073(Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 11))
                {
                    return Json(new { ErrorCode = 3 });
                }

                var obj = db.AgenMainAcc.Where(x => x.Status != 3).ToList();
                var mainAccList = obj.Select(x => new
                {
                    ID = x.AccountID,
                    Name = x.AccDesc,
                }).OrderBy(x => x.Name).ToList();
                
                return Json(new { ErrorCode = 0, MainAccList = mainAccList });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجى الاتصال بفريق الدعم الفني" });
            }
        }
    }
}