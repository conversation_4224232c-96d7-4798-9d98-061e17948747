(function () {
    'use strict';
    angular
        .module('App')
        .controller('d074', ['$scope', 'DataService', '$state', '$stateParams', 'Notification', 'blockUI', d074]);

    function d074($scope, DataService, $state, $stateParams, Notification, blockUI) {
        var vm = this;
        vm.AccountList = [];
        vm.MainObj = {};
        vm.DlFalg = 0;
        vm.Title = "";
        vm.DlTitle = "";
        vm.DlMessage = "";
        vm.SelectedID = "";
        vm.Status = 0;

 
        vm.initializeSession = function () {
            var sessionToken = localStorage.getItem('sessionToken');
            var userID = localStorage.getItem('userID');
            var userName = localStorage.getItem('userName');

            if (!sessionToken || !userID) {
                $state.go('LoginPage');
                return false;
            }

            blockUI.start();
            return DataService.a29002(sessionToken)
                .then(function (response) {
                    var data = response.data;
                    if (data.ErrorCode === 0) {
                        vm.UserID = data.UserID;
                        vm.UserName = data.UserName;
                        vm.sessionToken = sessionToken;
                        vm.Home = Home;
                        vm.AddNew = AddNew;
                        vm.Save = Save;
                        vm.BtnMainEdit = BtnMainEdit;
                        vm.d074003 = d074003;
                        vm.BtnStatus = BtnStatus;
                        vm.d074004 = d074004;
                        activate();
                        return DataService.a29004(sessionToken);
                    } else {
                        vm.clearSessionData();
                        $state.go('LoginPage');
                        return false;
                    }
                })
                .then(function (response) {
                    if (response && response.data.ErrorCode === 0) {
                        if (!response.data.Perms.med_percentage) {
                            Notification.error({
                                message: "لا تملك الصلاحيات المناسبة للوصول إلى هذه الصفحة",
                                delay: 2000,
                                positionY: "bottom",
                                positionX: "center"
                            });
                            vm.sys_page();
                            return false;
                        }
                        return true;
                    }
                    return false;
                })
                .catch(function () {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    return false;
                })
                .finally(function () {
                    blockUI.stop();
                });
        };

        vm.initializeSession();
        vm.clearSessionData = function () {
            localStorage.removeItem('sessionToken');
            localStorage.removeItem('userID');
            localStorage.removeItem('userName');
        };


        function activate() {
            blockUI.start();
            DataService.d074001(vm.UserID).then(function (response) {
                if (response.data.ErrorCode === 3) {
                    Notification.error({ message: 'ليس لديك صلاحية للوصول', delay: 2000 });
                    return;
                }
                vm.AccountList = response.data;
            }).catch(function (error) {
                Notification.error({ message: 'خطأ في تحميل البيانات', delay: 2000 });
            }).finally(function() {
                blockUI.stop();
            });
        }

        function Home() {
            $state.go('HomePage', {
                UserID: vm.UserID,
                UserName: vm.UserName
            });
        }

        function AddNew() {
            vm.MainObj = {};
            vm.DlFalg = 0;
            vm.Title = "إضافة تصنيف رئيسي جديد";
            vm.DlTitle = "إضافة تصنيف رئيسي جديد";
        }

        function Save() {

            blockUI.start();
            DataService.d074002(vm.MainObj, vm.UserID).then(function (response) {
                if (response.data.ErrorCode === 3) {
                    Notification.error({ message: 'ليس لديك صلاحية للوصول', delay: 2000 });
                    return;
                }
                if (response.data.error) {
                    Notification.error({ message: response.data.error, delay: 2000 });
                } else {
                    Notification.success({ message: 'تم الحفظ بنجاح', delay: 2000 });
                    activate();
                }
            }).catch(function (error) {
                Notification.error({ message: 'خطأ في حفظ البيانات', delay: 2000 });
            }).finally(function () {
                blockUI.stop();
            });

        }

        function BtnMainEdit(obj) {
            vm.MainObj = {
                AccountID: obj.ID,
                AccDesc: obj.Name,
            };
            vm.DlFalg = 1;
            vm.Title = "تعديل تصنيف رئيسي";
            vm.DlTitle = "تعديل تصنيف رئيسي";
            $('#NewItem').modal('show');
        }

        function d074003() {
           
                blockUI.start();
                DataService.d074003(vm.MainObj, vm.UserID).then(function (response) {
                    if (response.data.ErrorCode === 3) {
                        Notification.error({ message: 'ليس لديك صلاحية للوصول', delay: 2000 });
                        return;
                    }
                    if (response.data.error) {
                        Notification.error({ message: response.data.error, delay: 2000 });
                    } else {
                        Notification.success({ message: 'تم التعديل بنجاح', delay: 2000 });
                        activate();
                    }
                }).catch(function (error) {
                    Notification.error({ message: 'خطأ في تعديل البيانات', delay: 2000 });
                }).finally(function() {
                    blockUI.stop();
                });
           
        }

        function BtnStatus(obj, status) {
            vm.SelectedID = obj.ID;
            vm.Status = status;
            vm.DlTitle = status === 1 ? "تفعيل تصنيف رئيسي" :
                          status === 2 ? "إيقاف تصنيف رئيسي" :
                          "حذف تصنيف رئيسي";
            vm.DlMessage = status === 1 ? "هل تريد تفعيل التصنيف الرئيسي؟" :
                           status === 2 ? "هل تريد إيقاف التصنيف الرئيسي؟" :
                    "هل تريد حذف التصنيف الرئيسي؟";
            $("#Dl_Message").modal("show");
        }

        function d074004() {
            blockUI.start();
            DataService.d074004(vm.SelectedID, vm.Status, vm.UserID).then(function (response) {
                if (response.data.ErrorCode === 3) {
                    Notification.error({ message: 'ليس لديك صلاحية للوصول', delay: 2000 });
                    return;
                }
                if (response.data.error) {
                    Notification.error({ message: response.data.error, delay: 2000 });
                } else {
                    var message = vm.Status === 1 ? 'تم التفعيل بنجاح' :
                                 vm.Status === 2 ? 'تم الإيقاف بنجاح' :
                                 'تم الحذف بنجاح';
                    Notification.success({ message: message, delay: 2000 });
                    activate();
                }
            }).catch(function (error) {
                Notification.error({ message: 'خطأ في تنفيذ العملية', delay: 2000 });
            }).finally(function() {
                blockUI.stop();
            });
        }
    }
})();
