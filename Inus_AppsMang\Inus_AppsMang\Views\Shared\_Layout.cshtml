﻿@using Inus_AppsMang.Helpers
@{
    // Auto versioning for cache busting using the helper class
    var autoVersion = VersionHelper.GetEnvironmentVersion();
}
<!DOCTYPE html>
<html ng-app="App" lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <link rel="shortcut icon" href="~/Imges/management.png?v=@autoVersion" />

    <title>نظام إدارة منظومات التأمين</title>

    <!-- Base CSS -->
    @Styles.Render("~/Content/css")
    <link href="~/Content/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/dist/css/bootstrap-grid.min.css" rel="stylesheet" />
    <link href="~/Content/dist/css/bootstrap-reboot.min.css" rel="stylesheet" />
    <link href="~/Content/dist/css/bootstrap-utilities.min.css" rel="stylesheet" />
    <link href="~/Content/dist/css/bootstrap.rtl.min.css" rel="stylesheet" />
    <link href="~/Content/dist/css/bootstrap-utilities.rtl.min.css" rel="stylesheet" />
    <link href="~/Content/dist/css/bootstrap-reboot.rtl.min.css" rel="stylesheet" />
    <link href="~/Content/dist/css/bootstrap-grid.rtl.min.css" rel="stylesheet" />

    <!-- Font Awesome -->
    <link href="https://use.fontawesome.com/releases/v6.5.1/css/all.css" rel="stylesheet" />
    <link href="~/Content/fontawesome/css/icons.css" rel="stylesheet" />

    <!-- Extension CSS -->
    <link href="~/Content/ExtenCss/angular-block-ui.min.css" rel="stylesheet" />
    <link href="~/Content/ExtenCss/angular-ui-notification.min.css" rel="stylesheet" />
    <link href="~/Content/ExtenCss/app-theme.css" rel="stylesheet" />
    <link href="~/Content/ExtenCss/dropdowns.css" rel="stylesheet" />

    <!-- External Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Overpass:ital,wght@0,100;0,200;0,300;0,400;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- Icon Fonts - Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" crossorigin="anonymous">

    <!-- Base JavaScript -->
    <script src="~/Scripts/jquery-3.4.1.min.js"></script>
    <script src="~/Content/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/Scripts/angular.min.js"></script>

    <!-- Angular Extensions -->
    <script src="~/Scripts/ExtrnJs/ui-angular.min.js"></script>
    <script src="~/Scripts/ExtrnJs/angular-touch.min.js"></script>
    <script src="~/Scripts/ExtrnJs/angular-sanitize.min.js"></script>
    <script src="~/Scripts/ExtrnJs/angular-route.min.js"></script>
    <script src="~/Scripts/ExtrnJs/angular-animate.min.js"></script>
    <script src="~/Scripts/ExtrnJs/angular-ui-notification.min.js"></script>
    <script src="~/Scripts/ExtrnJs/angular-block-ui.min.js"></script>
    <script src="~/Scripts/ExtrnJs/angular-ui-router.min.js"></script>

    <script src="~/Scripts/chartJS/dist/Chart.min.js"></script>
    <script src="~/Scripts/chartJS/angular-chart.min.js"></script>

    <!-- Initialize Bootstrap Components -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize all Bootstrap components
            if (typeof bootstrap !== 'undefined') {
                // Initialize tooltips
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // Initialize popovers
                var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                popoverTriggerList.map(function (popoverTriggerEl) {
                    return new bootstrap.Popover(popoverTriggerEl);
                });
            }
        });
    </script>

    <!-- Application JavaScript -->
    <script src="~/FrontEnd/a30.js?v=20250620013500"></script>
    <script src="~/FrontEnd/a25.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a74.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a028.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a26.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a27.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a29.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a22.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a31.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a41.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a43.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a20.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a45.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a40.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a46.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a49.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a50.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a42.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a51.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a52.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a55.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a58.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a59.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a60.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a27Or.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/b058.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/b059.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/b060.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d061.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d062.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d063.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d064.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d065.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d066.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d067.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d068.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d069.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a27Co.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a27Tr.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a27Med.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d070.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d071.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d072.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d073.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a72.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/a73.js?v=@autoVersion"></script>
    <script src="~/FrontEnd/d074.js?v=@autoVersion"></script>
    <!-- Custom AngularJS Dropdown Solution -->
    <script>
        // Simplified dropdown solution with URL change monitoring
        document.addEventListener('DOMContentLoaded', function () {
            // Store dropdown states and prevent infinite loops
            var dropdownStates = {};
            var isInitializing = false;
            var currentUrl = window.location.href;

            function initializeDropdowns() {
                if (isInitializing) {
                    return;
                }

                isInitializing = true;

                // Clear all existing dropdown IDs to start fresh
                var existingDropdowns = document.querySelectorAll('[data-dropdown-id]');
                existingDropdowns.forEach(function (dropdown) {
                    dropdown.removeAttribute('data-dropdown-id');
                });
                dropdownStates = {};

                // Find all dropdown toggles
                var toggles = document.querySelectorAll('[data-bs-toggle="dropdown"], .dropdown-toggle');

                toggles.forEach(function (toggle, index) {
                    var dropdownId = 'dropdown_' + Date.now() + '_' + index;

                    toggle.setAttribute('data-dropdown-id', dropdownId);
                    dropdownStates[dropdownId] = false;

                    // Remove any existing event listeners by cloning
                    var newToggle = toggle.cloneNode(true);
                    toggle.parentNode.replaceChild(newToggle, toggle);

                    // Add custom click handler
                    newToggle.addEventListener('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation();

                        var clickedId = this.getAttribute('data-dropdown-id');

                        // Close all other dropdowns
                        Object.keys(dropdownStates).forEach(function (id) {
                            if (id !== clickedId) {
                                closeDropdown(id);
                            }
                        });

                        // Toggle this dropdown
                        if (dropdownStates[clickedId]) {
                            closeDropdown(clickedId);
                        } else {
                            openDropdown(clickedId);
                        }
                    });
                });

                setTimeout(function () {
                    isInitializing = false;
                }, 100);
            }

            function openDropdown(dropdownId) {
                var toggle = document.querySelector('[data-dropdown-id="' + dropdownId + '"]');
                if (!toggle) return;

                var dropdown = toggle.closest('.dropdown');
                var menu = dropdown ? dropdown.querySelector('.dropdown-menu') : null;

                if (dropdown && menu) {
                    menu.style.display = 'block';
                    menu.classList.add('show');
                    dropdown.classList.add('show');
                    toggle.setAttribute('aria-expanded', 'true');
                    dropdownStates[dropdownId] = true;

                    // Smart positioning to ensure dropdown appears below button
                    setTimeout(function () {
                        var rect = toggle.getBoundingClientRect();
                        var menuRect = menu.getBoundingClientRect();
                        var viewportHeight = window.innerHeight;

                        // Check if dropdown would appear above the button
                        if (menuRect.top < rect.bottom) {
                            // Force dropdown to appear below the button
                            menu.style.position = 'absolute';
                            menu.style.top = '100%';
                            menu.style.transform = 'none';

                            // Handle right-aligned dropdowns
                            if (menu.classList.contains('dropdown-menu-end')) {
                                menu.style.right = '0';
                                menu.style.left = 'auto';
                            } else {
                                menu.style.left = '0';
                                menu.style.right = 'auto';
                            }
                        } else {
                            // Default positioning is working, clear manual styles
                            menu.style.position = '';
                            menu.style.top = '';
                            menu.style.left = '';
                            menu.style.right = '';
                            menu.style.transform = '';
                        }

                        menu.style.zIndex = '1050';
                    }, 10);
                }
            }

            function closeDropdown(dropdownId) {
                var toggle = document.querySelector('[data-dropdown-id="' + dropdownId + '"]');
                if (!toggle) return;

                var dropdown = toggle.closest('.dropdown');
                var menu = dropdown ? dropdown.querySelector('.dropdown-menu') : null;

                if (dropdown && menu) {
                    menu.style.display = 'none';
                    menu.classList.remove('show');
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                    dropdownStates[dropdownId] = false;
                }
            }

            function closeAllDropdowns() {
                Object.keys(dropdownStates).forEach(function (id) {
                    closeDropdown(id);
                });
            }

            // Close dropdowns when clicking outside
            document.addEventListener('click', function (e) {
                if (!e.target.closest('.dropdown')) {
                    closeAllDropdowns();
                }
            });

            // Close on escape key
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape' || e.keyCode === 27) {
                    closeAllDropdowns();
                }
            });

            // Monitor for URL changes and DOM changes
            function monitorChanges() {
                // Check for URL changes
                if (window.location.href !== currentUrl) {
                    currentUrl = window.location.href;
                    closeAllDropdowns();
                    setTimeout(initializeDropdowns, 500);
                }

                // Check for new dropdown elements
                var uninitializedDropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]:not([data-dropdown-id]), .dropdown-toggle:not([data-dropdown-id])');
                if (uninitializedDropdowns.length > 0) {
                    setTimeout(initializeDropdowns, 100);
                }
            }

            // Initial setup
            setTimeout(initializeDropdowns, 1000);
            setTimeout(initializeDropdowns, 3000);
            setTimeout(initializeDropdowns, 5000);

            // Monitor changes every second
            setInterval(monitorChanges, 1000);

            // Also monitor when the page becomes visible
            document.addEventListener('visibilitychange', function () {
                if (!document.hidden) {
                    setTimeout(initializeDropdowns, 500);
                }
            });

            // Monitor for hash changes
            window.addEventListener('hashchange', function () {
                closeAllDropdowns();
                setTimeout(initializeDropdowns, 500);
            });

            // Monitor for popstate (back/forward navigation)
            window.addEventListener('popstate', function () {
                closeAllDropdowns();
                setTimeout(initializeDropdowns, 500);
            });

            // Listen for permissions loaded event
            document.addEventListener('permissionsLoaded', function () {
                setTimeout(initializeDropdowns, 200);
            });

            // Expose reinitialize function globally
            window.reinitializeDropdowns = function () {
                setTimeout(initializeDropdowns, 100);
            };
        });
    </script>
</head>
<body class="vh-100">
    @Html.AntiForgeryToken()
    <div class="container-fluid">
        <ui-view>
            @RenderBody()
        </ui-view>
    </div>
</body>
</html>
