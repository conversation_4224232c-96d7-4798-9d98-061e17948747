﻿(function () {
    'use strict';
    angular.module("App").factory("DataService", ["$http", DataService]);


    function DataService($http) {


        return {
            a59001: a59001,
            a59002: a59002,
            a59003: a59003,
            a59004: a59004,
            a59005: a59005,
            a59006: a59006,
            a61001: a61001,
            a61002: a61002,
            a61003: a61003,
            a61004: a61004,
            a61005: a61005,
            a61006: a61006,
            a61007: a61007,
            a61008: a61008,
            a61009: a61009,
            a72001: a72001,
            a72003: a72003,
            a72004: a72004,
            a72005: a72005,
            a72006: a72006,
            a72007: a72007,
            a72008: a72008,
            a73001: a73001,
            a73002: a73002,
            a73003: a73003,
            a580005: a580005,
            a580004: a580004,
            a580003: a580003,
            a580002: a580002,
            //a570011: a570011,
            //a570009: a570009,
            //a570008: a570008,
            //a570004: a570004,
            //a570003: a570003,
            //a560012: a560012,
            //a560011: a560011,
            //a560010: a560010,
            //a560009: a560009,
            //a560008: a560008,
            //a560007: a560007,
            //a560006: a560006,
            a5500016: a5500016,
            a5500015: a5500015,
            a5500014: a5500014,
            a5500013: a5500013,
            a5500012: a5500012,
            a5500011: a5500011,
            a5500010: a5500010,
            a550009: a550009,
            a550008: a550008,
            a550007: a550007,
            a550006: a550006,
            a550005: a550005,
            a550004: a550004,
            a550003: a550003,
            a550002: a550002,
            //a540004: a540004,
            //a540003: a540003,
            //a540002: a540002,
            a520011: a520011,
            a520010: a520010,
            a52009: a52009,
            a52008: a52008,
            a52007: a52007,
            a52006: a52006,
            a52005: a52005,
            a52004: a52004,
            a52003: a52003,
            a52002: a52002,
            a41005: a41005,
            a41006: a41006,
            a27016: a27016,
            a27015: a27015,
            a29001: a29001,
            a29002: a29002,
            a29003: a29003,
            a29004: a29004,
            a25004: a25004,
            a25003: a25003,
            a25002: a25002,
            a25005: a25005,
            IsExisit: IsExisit,
            a25006: a25006,
            a41002: a41002,
            a41003: a41003,
            a26011: a26011,
            a23004: a23004,
            a250055: a250055,
            a23003: a23003,
            a23005: a23005,
            a23006: a23006,
            a21004: a21004,
            a21003: a21003,
            a21006: a21006,
            a21005: a21005,
            a21007: a21007,
            a30001: a30001,
            a30002: a30002,
            a30003: a30003,
            a30004: a30004,
            a43012: a43012,
            a43015: a43015,
            a43017: a43017,
            a43002: a43002,
            a43003: a43003,
            a43004: a43004,
            a43005: a43005,
            a43006: a43006,
            a43007: a43007,
            a43008: a43008,
            a43009: a43009,
            a43010: a43010,
            a43011: a43011,
            a43012: a43012,
            a43020: a43020,
            a43019: a43019,
            a20004: a20004,
            a20003: a20003,
            a20006: a20006,
            a45005: a45005,
            a45004: a45004,
            a45003: a45003,
            a20005: a20005,
            a45006: a45006,
            a51005: a51005,
            a51004: a51004,
            a400030: a400030,
            a4000027: a4000027,
            a4000025: a4000025,
            a4000026: a4000026,
            a400021: a400021,
            a400011: a400011,
            a40001: a40001,
            a40073: a40073,
            a40002: a40002,
            a51006: a51006,
            a40003: a40003,
            a40009: a40009,
            a40008: a40008,
            a40007: a40007,
            a40006: a40006,
            a40005: a40005,
            a40004: a40004,
            a40010: a40010,
            a46004: a46004,
            a46005: a46005,
            a46006: a46006,
            a460010: a460010,
            a460011: a460011,
            a460012: a460012,
            a460021: a460021,
            a49008: a49008,
            a49007: a49007,
            a49007: a49007,
            a490025: a490025,
            a490011: a490011,
            a49003: a49003,
            a490023: a490023,
            a49002: a49002,
            a50001: a50001,
            a50003: a50003,
            a50004: a50004,
            a50005: a50005,
            a42008: a42008,
            a42009: a42009,
            a42002: a42002,
            a400021: a400021,
            a4000027: a4000027,
            a410016: a410016,
            a41007: a41007,
            a400031: a400031,
            a400032: a400032,
            a400033: a400033,
            a260056: a260056,
            a250057: a250057,
            a260058: a260058,
            a250013: a250013,
            a510002: a510002,
            a490017: a490017,
            a490030: a490030,
            a490031: a490031,
            a4900032: a4900032,
            b058002: b058002,
            b058003: b058003,
            b058004: b058004,
            b059002: b059002,
            b059003: b059003,
            b059004: b059004,
            d061002: d061002,
            d061003: d061003,
            d061004: d061004,
            d062002: d062002,
            d062003: d062003,
            d062004: d062004,
            d063002: d063002,
            d063003: d063003,
            d063004: d063004,
            a250025: a250025,
            a260059: a260059,
            /*  a260070: a260070,*/
            a260071: a260071,
            d64004: d64004,
            d64003: d64003,
            d64005: d64005,
            d64006: d64006,
            d65004: d65004,
            d65003: d65003,
            d65005: d65005,
            d65006: d65006,
            d66004: d66004,
            d66003: d66003,
            d66005: d66005,
            d66006: d66006,
            d67004: d67004,
            d67003: d67003,
            d67005: d67005,
            d67006: d67006,
            a70006: a70006,
            d0680002: d0680002,
            d0680003: d0680003,
            d0680004: d0680004,
            d0680005: d0680005,
            d0680006: d0680006,
            d0680007: d0680007,
            d0680008: d0680008,
            d0680009: d0680009,
            d06800010: d06800010,
            d06800011: d06800011,
            d06800012: d06800012,
            d06800013: d06800013,
            d06800014: d06800014,
            d06800015: d06800015,
            d06800016: d06800016,
            d0710002: d0710002,
            d0710003: d0710003,
            d0710004: d0710004,
            d0710005: d0710005,
            d0710006: d0710006,
            d0710007: d0710007,
            d0710008: d0710008,
            d0710009: d0710009,
            d07100010: d07100010,
            d07100011: d07100011,
            d07100012: d07100012,
            d07100013: d07100013,
            d07100014: d07100014,
            d07100015: d07100015,
            d07100016: d07100016,
            d069002: d069002,
            d069003: d069003,
            d069004: d069004,
            a400059: a400059,
            a000050: a000050,
            a000051: a000051,
            a000052: a000052,
            a400071: a400071,
            a400072: a400072,
            a27054: a27054,
            a27056: a27056,
            d070001: d070001,
            d070002: d070002,
            d070003: d070003,
            a27050: a27050,
            a41011: a41011,
            a41010: a41010,
            a40013: a40013,
            a490050: a490050,
            a490054: a490054,
            a410021: a410021,
            a410022: a410022,
            a74001: a74001,
            a74002: a74002,
            a74003: a74003,
            a74004: a74004,
            d072001: d072001,
            d072002: d072002,
            d072003: d072003,
            d072004: d072004,
            d073001: d073001,
            d073002: d073002,
            d073003: d073003,
            d073004: d073004,
            d074001: d074001,
            d074002: d074002,
            d074003: d074003,
            d074004: d074004,
        }

        function a410022(UserID, selectedAgencyID, selectedMonth, SelectedYear, InsTypeNo) {
            return $http.post("/a41/a410022 ", { UserID, selectedAgencyID, selectedMonth, SelectedYear, InsTypeNo })
        }
        function a410021(UserID, SysTotals, Flag) {
            return $http.post("/a41/a410021 ", { UserID, SysTotals, Flag })
        }
        function a490054(ID, UserID) {
            return $http.post("/a49/a490054 ", { ID, UserID })
        }
        function a490050(ID, UserID) {
            return $http.post("/a49/a490050 ", { ID, UserID })
        }
        function a41010(UserID, SelectedYear) {
            return $http.post("/a41/a41010 ", { UserID, SelectedYear })
        }
        function a41011(UserID) {
            return $http.post("/a41/a41011 ", { UserID })
        }
        function a40013(UserID, SelectedYear, selectedMonth) {
            return $http.post("/a41/a40013 ", { UserID, SelectedYear ,selectedMonth })
        }
        function a400059(agencyId) {
            return $http.post("/a40/a400059 ", { agencyId })
        }
        function a400072(AgencyID, UserID) {
            return $http.post("/a40/a400072 ", { AgencyID, UserID })


        }
        function a400059(AgPerObj) {
            return $http.post("/a40/a400059 ", { AgPerObj })
        }
        function a400071(AgPerObj) {
            return $http.post("/a40/a400071 ", { AgPerObj })
        }
        function a000052(UserID, AgUserID, ID) {
            return $http.post("/a49/a000052 ", { UserID, AgUserID, ID })
        }
        function a000051(ID, UserID) {
            return $http.post("/a49/a000051 ", { ID, UserID })
        }
        function a000050(ID, UserID) {
            return $http.post("/a49/a000050 ", { ID, UserID })

        }

        function d069004(UserID) {
            return $http.post("/d069/d069004 ", { UserID })
        }
        function d069003(UserID) {
            return $http.post("/d069/d069003 ", { UserID })
        }
        function d069002(UserID) {
            return $http.post("/d069/SendPostRequest ", { UserID })
        }

        function d06800016(UserID) {
            return $http.post("/d068/d06800016 ", { UserID })
        }
        function d06800015(UserID) {
            return $http.post("/d068/d06800015 ", { UserID })
        }
        function d06800014(UserID, AdType) {
            return $http.post("/d068/d06800014 ", { UserID, AdType })
        }
        function d06800013(MainObj) {
            return $http.post("/d068/d06800013", { MainObj })
        }
        function d06800012(UserID) {
            return $http.post("/d068/d06800012", { UserID })
        }
        function d06800011(ID, UserID) {
            return $http.post("/d068/d06800011", { ID, UserID })
        }
        function d06800010(UserID) {
            return $http.post("/d068/d06800010", { UserID })
        }
        function d0680009(SelectedSerID, UserID) {
            return $http.post("/d068/d0680009", { SelectedSerID, UserID })
        }
        function d0680008(MainObj) {
            return $http.post("/d068/d0680008", { MainObj })
        }
        function d0680007(SerialsGivID, UserID) {
            return $http.post("/d068/d0680007", { SerialsGivID, UserID })

        }
        function d0680006(NumOFSer, SeStart, UserID) {
            return $http.post("/d068/d0680006", { NumOFSer, SeStart, UserID })
        }
        function d0680005(SgID, UserID) {
            return $http.post("/d068/d0680005", { SgID, UserID })
        }
        function d0680004(MainObj) {
            return $http.post("/d068/d0680004", { MainObj })
        }
        function d0680003() {
            return $http.post("/d068/d0680003")
        }
        function d0680002(PrintObj) {
            return $http.post("/d068/d0680002", { PrintObj })
        }
        
        // Orange Inventory (d071) HTTP requests
        function d07100016(UserID) {
            return $http.post("/d071/d07100016 ", { UserID })
        }
        function d07100015(UserID) {
            return $http.post("/d071/d07100015 ", { UserID })
        }
        function d07100014(UserID, AdType) {
            return $http.post("/d071/d07100014 ", { UserID, AdType })
        }
        function d07100013(MainObj) {
            return $http.post("/d071/d07100013", { MainObj })
        }
        function d07100012(UserID) {
            return $http.post("/d071/d07100012", { UserID })
        }
        function d07100011(ID, UserID) {
            return $http.post("/d071/d07100011", { ID, UserID })
        }
        function d07100010(UserID) {
            return $http.post("/d071/d07100010", { UserID })
        }
        function d0710009(SelectedSerID, UserID) {
            return $http.post("/d071/d0710009", { SelectedSerID, UserID })
        }
        function d0710008(MainObj) {
            return $http.post("/d071/d0710008", { MainObj })
        }
        function d0710007(SerialsGivID, UserID) {
            return $http.post("/d071/d0710007", { SerialsGivID, UserID })
        }
        function d0710006(NumOFSer, SeStart, UserID) {
            return $http.post("/d071/d0710006", { NumOFSer, SeStart, UserID })
        }
        function d0710005(SgID, UserID) {
            return $http.post("/d071/d0710005", { SgID, UserID })
        }
        function d0710004(MainObj) {
            return $http.post("/d071/d0710004", { MainObj })
        }
        function d0710003() {
            return $http.post("/d071/d0710003")
        }
        function d0710002(PrintObj) {
            return $http.post("/d071/d0710002", { PrintObj })
        }
        function d67006(ID, Status, UserID) {
            return $http.post("/d067/d67006", { ID, Status, UserID })
        }
        function d67005(MainObj) {
            return $http.post("/d067/d67005", { MainObj })
        }
        function d67003(MainObj) {
            return $http.post("/d067/d67003", { MainObj })
        }
        function d67004(IsAll, UserID) {
            return $http.post("/d067/d67004", { IsAll, UserID })
        }
        function d66006(ID, Status, UserID) {
            return $http.post("/d066/d66006", { ID, Status, UserID })
        }
        function d66005(MainObj) {
            return $http.post("/d066/d66005", { MainObj })
        }
        function d66003(MainObj) {
            return $http.post("/d066/d66003", { MainObj })
        }
        function d66004(IsAll, UserID) {
            return $http.post("/d066/d66004", { IsAll, UserID })
        }
        function d65006(ID, Status, UserID) {
            return $http.post("/d065/d65006", { ID, Status, UserID })
        }
        function d65005(MainObj) {
            return $http.post("/d065/d65005", { MainObj })
        }
        function d65003(MainObj) {
            return $http.post("/d065/d65003", { MainObj })
        }
        function d65004(IsAll, UserID) {
            return $http.post("/d065/d65004", { IsAll, UserID })
        }
        function d64006(ID, Status, UserID) {
            return $http.post("/d064/d64006", { ID, Status, UserID })
        }
        function d64005(MainObj) {
            return $http.post("/d064/d64005", { MainObj })
        }
        function d64003(MainObj) {
            return $http.post("/d064/d64003", { MainObj })
        }
        function d64004(IsAll, UserID) {
            return $http.post("/d064/d64004", { IsAll, UserID })

        }

        function a260071(PerObj) {
            return $http.post("/a26/a260071 ", { PerObj })
        }
        function a260059(UserID) {
            return $http.post("/a26/a260059 ", { UserID })
        }
        function a250025(logedInID, selectedUserID) {
            return $http.post("/a26/a250025 ", { logedInID, selectedUserID })
        }
        function d063004(UserID) {
            return $http.post("/d063/d063004 ", { UserID })
        }
        function d063003(UserID) {
            return $http.post("/d063/d063003 ", { UserID })
        }
        function d063002(UserID) {
            return $http.post("/d063/SendPostRequest ", { UserID })
        }
        function d062004(UserID) {
            return $http.post("/d062/d062004 ", { UserID })
        }
        function d062003(UserID) {
            return $http.post("/d062/d062003 ", { UserID })
        }
        function d062002(UserID) {
            return $http.post("/d062/SendPostRequest ", { UserID })
        }
        function d061004(UserID) {
            return $http.post("/d061/d061004 ", { UserID })
        }
        function d061003(UserID) {
            return $http.post("/d061/d061003 ", { UserID })
        }
        function d061002(UserID) {
            return $http.post("/d061/SendPostRequest ", { UserID })
        }
        function b059004(UserID) {
            return $http.post("/b059/b059004 ", { UserID })
        }
        function b059003(UserID) {
            return $http.post("/b059/b059003 ", { UserID })
        }
        function b059002(UserID) {
            return $http.post("/b059/SendPostRequest ", { UserID })
        }
        function b058004(UserID) {
            return $http.post("/b058/b058004 ", { UserID })
        }
        function b058003(UserID) {
            return $http.post("/b058/b058003 ", { UserID })
        }
        function b058002(UserID) {
            return $http.post("/b058/SendPostRequest ", { UserID })
        }
        function a4900032(UserID, AgUserID, ID) {
            return $http.post("/a49/a4900032 ", { UserID, AgUserID, ID })
        }
        function a490031(UserID, AgUserID, Search) {
            return $http.post("/a49/a490031 ", { UserID, AgUserID, Search })
        }
        function a490030(obj) {
            return $http.post("/a49/a490030 ", { obj })
        }
        function a490017(userName, passWord) {
            return $http.post("/a49/a490017 ", { userName, passWord })
        }
        function a580005(UserID) {
            return $http.post("/a58/a580005 ", { UserID })
        }
        function a580004(UserID, MainObj) {
            return $http.post("/a58/a580004 ", { UserID, MainObj })
        }
        function a580003(UserID, MainObj) {
            return $http.post("/a58/a580003 ", { UserID, MainObj })
        }
        function a580002(UserID, MainObj) {
            return $http.post("/a58/a580002 ", { UserID, MainObj })
        }
        //function a570011(logedInID) {
        //    return $http.post("/a57/a570011 ", { logedInID })
        //}
        //function a570009(logedInID) {
        //    return $http.post("/a57/a570009 ", { logedInID })
        //}
        //function a570008(DocID, UserID) {
        //    return $http.post("/a57/a570008 ", { DocID, UserID })
        //}
        //function a570003(logedInID) {
        //    return $http.post("/a57/a570003 ", { logedInID })
        //}
        //function a560012(UserID, MainObj) {
        //    return $http.post("/a56/a560012 ", { UserID, MainObj })
        //}
        //function a560011(UserID, MainObj) {
        //    return $http.post("/a56/a560011 ", { UserID, MainObj })
        //}
        //function a560010(UserID, MainObj) {
        //    return $http.post("/a56/a560010 ", { UserID, MainObj })
        //}
        //function a560009(UserID) {
        //    return $http.post("/a56/a560009 ", { UserID })
        //}
        //function a560008(MainObj) {
        //    return $http.post("/a56/a560008 ", { MainObj })
        //}
        //function a560007(dateFrom, fromTo) {
        //    return $http.post("/a56/a560007 ", { dateFrom, fromTo })
        //}
        //function a560006() {
        //    return $http.post("/a56/a560006")
        //}
        function a5500016(UserID) {
            return $http.post("/a55/a5500016 ", { UserID })
        }
        function a5500015(UserID) {
            return $http.post("/a55/a5500015 ", { UserID })
        }
        function a5500014(UserID, AdType) {
            return $http.post("/a55/a5500014 ", { UserID, AdType })
        }
        function a5500013(MainObj) {
            return $http.post("/a55/a5500013", { MainObj })
        }
        function a5500012(UserID) {
            return $http.post("/a55/a5500012", { UserID })
        }
        function a5500011(ID, UserID) {
            return $http.post("/a55/a5500011", { ID, UserID })
        }
        function a5500010(UserID) {
            return $http.post("/a55/a5500010", { UserID })
        }
        function a550009(SelectedSerID, UserID) {
            return $http.post("/a55/a550009", { SelectedSerID, UserID })
        }
        function a550008(MainObj) {
            return $http.post("/a55/a550008", { MainObj })
        }
        function a550007(SerialsGivID, UserID) {
            return $http.post("/a55/a550007", { SerialsGivID, UserID })

        }
        function a550006(NumOFSer, SeStart, UserID) {
            return $http.post("/a55/a550006", { NumOFSer, SeStart, UserID })
        }
        function a550005(SgID, UserID) {
            return $http.post("/a55/a550005", { SgID, UserID })
        }
        function a550004(MainObj) {
            return $http.post("/a55/a550004", { MainObj })
        }
        function a550003() {
            return $http.post("/a55/a550003")
        }
        function a550002(PrintObj) {
            return $http.post("/a55/a550002", { PrintObj })
        }
        function a540004(UserID) {
            return $http.post("/a54/a540004 ", { UserID })
        }
        function a540003(UserID, ID2, CID) {
            return $http.post("/a54/a540003 ", { UserID, ID2, CID })
        }
        function a540002(UserID, Mainobj) {
            return $http.post("/a54/a540002 ", { UserID, Mainobj })
        }
        function a520011(IsAll, UserID) {
            return $http.post("/a52/a520011", { IsAll, UserID })
        }
        function a520010(mainObj, Perms) {
            return $http.post("/a52/a520010 ", { mainObj, Perms })
        }
        function a52009(MainObj, Perms) {
            return $http.post("/a52/a52009 ", { MainObj, Perms })
        }
        function a52008(SelectedCntryID, Status, UserID) {
            return $http.post("/a52/a21006 ", { SelectedCntryID, Status, UserID })
        }
        function a52007(IsAll, UserID) {
            return $http.post("/a52/a52007", { IsAll, UserID })
        }
        function a52006(IsAll, CntryID, UserID) {
            return $http.post("/a52/a52006", { IsAll, CntryID, UserID })
        }
        function a52005(mainObj, CntryID, Perms) {
            return $http.post("/a52/a52005 ", { mainObj, CntryID, Perms })
        }
        function a52004(MainObj, Perms) {
            return $http.post("/a52/a52004 ", { MainObj, Perms })
        }
        function a52003(SelectedcitID, Status, UserID) {
            return $http.post("/a52/a52003 ", { SelectedcitID, Status, UserID })
        }
        function a52002() {
            return $http.post("/a52/a52002 ")
        }
        function a510002(DFrom, DTo) {
            return $http.post("/a51/a510002 ", { DFrom, DTo })
        }
        function a250013(logedInID) {
            return $http.post("/a26/a250013 ", { logedInID })
        }
        function a260058(AgencyIDs, UserID, InsertBy) {
            return $http.post("/a26/a260058", { AgencyIDs, UserID, InsertBy })
        }
        function a250057(SelectedUserID, UserID) {
            return $http.post("/a26/a250057", { SelectedUserID, UserID })
        }
        function a260056(SelectedUserID, UserID) {
            return $http.post("/a26/a260056", { SelectedUserID, UserID })
        }

        function a400033(AgencyID, Comp_ID, UserID) {
            return $http.post("/a40/a400033", { AgencyID, Comp_ID, UserID })
        }
        function a400031(AgencyID, UserID) {
            return $http.post("/a40/a400031", { AgencyID, UserID })
        }
        function a400032(ID, UserID) {
            return $http.post("/a40/a400032", { ID, UserID })
        }
        function a4000027(obj) {
            return $http.post("/a40/a400021", { obj })
        }
        function a400021(AgencyID, UserID) {
            return $http.post("/a40/a400021", { AgencyID, UserID })
        }
        function a42002(UserID, MainObj, Li) {
            return $http.post("/a42/a42002", { UserID, MainObj, Li })
        }
        function a42009(UserID) {
            return $http.post("/a42/a42009", { UserID })
        }

        function a42008(UserID) {
            return $http.post("/a42/a42008", { UserID })
        }
        function a50005(ID, Status, UserID) {
            return $http.post("/a50/a50005", { ID, Status, UserID })
        }
        function a50004(MainObj) {
            return $http.post("/a50/a50004", { MainObj })
        }
        function a50003(MainObj) {
            return $http.post("/a50/a50003", { MainObj })
        }
        function a50001(UserID) {
            return $http.post("/a50/a50001 ", { UserID })
        }
        function a490025(DocID, UserID) {
            return $http.post("/a49/a450025 ", { DocID, UserID })
        }
        function a490011(DocID, UserID) {
            return $http.post("/a49/a450011 ", { DocID, UserID })
        }
        function a49003(DocID, UserID) {
            return $http.post("/a49/a49003 ", { DocID, UserID })
        }
        function a490023(DocID, UserID) {
            return $http.post("/a49/a450023 ", { DocID, UserID })
        }
        function a49002(logedInID) {
            return $http.post("/a49/a49002 ", { logedInID })
        }
        function a49008(logedInID) {
            return $http.post("/a49/a45008 ", { logedInID })
        }
        function a49007(logedInID) {
            return $http.post("/a49/a45007 ", { logedInID })
        }
        function a460021(MainObj) {
            return $http.post("/a46/a460021", { MainObj })
        }
        function a460012(ID, Status, UserID) {
            return $http.post("/a46/a460012", { ID, Status, UserID })
        }
        function a460011(MainObj) {
            return $http.post("/a46/a460011", { MainObj })
        }
        function a460010(ID, UserID) {
            return $http.post("/a46/a460010", { ID, UserID })
        }

        function a46006(ID, Status, UserID) {
            return $http.post("/a46/a46006", { ID, Status, UserID })
        }
        function a46005(MainObj) {
            return $http.post("/a46/a46005", { MainObj })
        }
        function a46004(UserID) {
            return $http.post("/a46/a46004 ", { UserID })
        }
        function a40010(UserID, AgencyType) {
            return $http.post("/a40/a40010 ", { UserID, AgencyType })
        }

        function a40009(obj, Status, UserID) {
            return $http.post("/a40/a40009", { obj, Status, UserID })
        }
        function a40008(obj, UserID) {
            return $http.post("/a40/a40008", { obj, UserID })
        }
        function a40007(Obj, UserID) {
            return $http.post("/a40/a40007", { Obj, UserID })
        }
        function a40006(Obj, UserID) {
            return $http.post("/a40/a40006", { Obj, UserID })
        }
        function a40005(Obj, UserID) {
            return $http.post("/a40/a40005", { Obj, UserID })
        }
        function a40004(FID) {
            return $http.post("/a40/a40004", { FID })
        }
        function a51006(Obj, UserID) {
            return $http.post("/a40/a40006", { Obj, UserID })
        }
        function a51005(Obj, UserID) {
            return $http.post("/a40/a40005", { Obj, UserID })
        }
        function a51004(FID) {
            return $http.post("/a40/a40004", { FID })
        }
        function a400030(AgUId, Status, UserID) {
            return $http.post("/a40/a400034", { AgUId, Status, UserID })
        }
        function a4000027(obj) {
            return $http.post("/a40/a4000027", { obj })
        }
        function a4000025(obj) {
            return $http.post("/a40/a4000025", { obj })
        }
        function a4000026(UserName, AgUId, UserID) {
            return $http.post("/a40/a4000026", { UserName, AgUId, UserID })
        }
        function a400021(ID, UserID) {
            return $http.post("/a40/a400021", { ID, UserID })

        }
        function a400011() {
            return $http.post("/a40/a400012 ")
        }
        function a40003() {
            return $http.post("/a40/a40003")
        }

        function a40002(Obj, UserID) {
            return $http.post("/a40/a40002", { Obj, UserID })
        }
        function a40001(UserID) {
            return $http.post("/a40/a40001", { UserID })
        }
        function a40073(UserID) {
            return $http.post("/a40/a40073", { UserID })
        }
        function a45006(ID, Status, UserID) {
            return $http.post("/a45/a45006", { ID, Status, UserID })
        }
        function a41007(UserID, Mainobj) {
            return $http.post("/a41/a41007", { UserID, Mainobj })
        }
        function a41005(UserID) {
            return $http.post("/a41/a41005", { UserID })
        }
        function a410016(UserID, ID2) {
            return $http.post("/a41/a41006", { UserID, ID2 })
        }
        function a41006(IsAll, UserID) {
            return $http.post("/a45/a41006", { IsAll, UserID })
        }

        function a45004(IsAll, UserID) {
            return $http.post("/a45/a45004", { IsAll, UserID })
        }
        function a45003(MainObj) {
            return $http.post("/a45/a45003", { MainObj })
        }
        function a45005(MainObj) {
            return $http.post("/a45/a45005", { MainObj })
        }
        function a20006(ID, Status, UserID) {
            return $http.post("/a20/a20006", { ID, Status, UserID })
        }

        function a20003(MainObj) {
            return $http.post("/a20/a20003", { MainObj })
        }
        function a250055(SelectedAg, UnSelectedAg, SelectedUserID, UserID) {
            return $http.post("/a26/a250055", { SelectedAg, UnSelectedAg, SelectedUserID, UserID })
        }
        function a20005(MainObj) {
            return $http.post("/a20/a20005", { MainObj })
        }
        function a20004(IsAll, UserID) {
            return $http.post("/a20/a20004", { IsAll, UserID })
        }


        function a43020(PrintObj) {
            return $http.post("/a43/a43020", { PrintObj })
        }
        function a43019() {
            return $http.post("/a43/a43019")
        }
        function a43017(MainObj) {
            return $http.post("/a43/a43017", { MainObj })
        }
        function a43015(SgID, UserID) {
            return $http.post("/a43/a43015", { SgID, UserID })
        }
        function a43012(NumOFSer, SeStart, UserID) {
            return $http.post("/a43/a43012", { NumOFSer, SeStart, UserID })
        }
        function a43011(SerialsGivID, UserID) {
            return $http.post("/a43/a43011", { SerialsGivID, UserID })

        }
        function a43010(MainObj) {
            return $http.post("/a43/a43010", { MainObj })
        }
        function a43009(SelectedSerID, UserID) {
            return $http.post("/a43/a43009", { SelectedSerID, UserID })
        }
        function a43008(UserID) {
            return $http.post("/a43/a43008", { UserID })
        }
        function a43007(ID, UserID) {
            return $http.post("/a43/a43007", { ID, UserID })
        }
        function a70006(UserID) {
            return $http.post("/a70/a43006", { UserID })
        }
        function a43006(UserID) {
            return $http.post("/a43/a43006", { UserID })
        }
        function a43005(MainObj) {
            return $http.post("/a43/a43005", { MainObj })
        }
        function a43006(UserID) {
            return $http.post("/a43/a43006", { UserID })
        }
        function a43005(MainObj) {
            return $http.post("/a43/a43005", { MainObj })
        }
        function a43004(UserID, AdType) {
            return $http.post("/a43/a43004 ", { UserID, AdType })
        }
        function a43003(UserID) {
            return $http.post("/a43/a43003 ", { UserID })
        }
        function a43002(UserID) {
            return $http.post("/a43/a43002 ", { UserID })
        }
        function a30004(Mainobj, UserID) {
            return $http.post("/a31/a30004", { Mainobj, UserID })
        }
        function a30003(Mainobj, Status, UserID) {
            return $http.post("/a31/a30003", { Mainobj, Status, UserID })
        }
        function a30002(Mainobj, UserID) {
            return $http.post("/a31/a30002", { Mainobj, UserID })
        }
        function a30001(UserID) {
            return $http.post("/a31/a30001", { UserID })
        }
        function a21007(MainObj, IsAll, UserID) {
            return $http.post("/a22/a21007", { MainObj, IsAll, UserID })
        }

        function a21006(SelectedCntryID, Status, UserID) {
            return $http.post("/a22/a21006 ", { SelectedCntryID, Status, UserID })
        }

        function a21005(MainObj, Perms) {
            return $http.post("/a22/a21005 ", { MainObj, Perms })
        }

        function a21003(mainObj) {
            return $http.post("/a22/a21003 ", { mainObj })
        }
        function a21004(IsAll, UserID) {
            return $http.post("/a22/a21004", { IsAll, UserID })
        }
        function a23006(SelectedcitID, Status, UserID) {
            return $http.post("/a22/a23006 ", { SelectedcitID, Status, UserID })
        }
        function a23005(MainObj, Perms) {
            return $http.post("/a22/a23005 ", { MainObj, Perms })
        }

        function a23003(mainObj, CntryID, Perms) {
            return $http.post("/a22/a23003 ", { mainObj, CntryID, Perms })
        }
        function a23004(IsAll, CntryID, UserID) {
            return $http.post("/a22/a23004", { IsAll, CntryID, UserID })
        }
        function a27050(UserID) {
            return $http.post("/a27/a27050 ", { UserID })
        }
        function a27056(settingsObj) {
            return $http.post("/a27/a27056 ", { settingsObj })
        }
        function a27054(UserID) {
            return $http.post("/a27/a27054 ", { UserID })
        }
        function a41003(DurSt, AgeSt, UserID) {
            return $http.post("/a27/a41003 ", {  DurSt, AgeSt, UserID })
        }
        function a41002(UserID) {
            return $http.post("/a27/a41002 ", { UserID })
        }
        function a26011(UserID) {
            return $http.post("/a27/a26011 ", { UserID })
        }
        function a25006(SelectedUserID, Status, UserID) {
            return $http.post("/a26/a25006 ", { SelectedUserID, Status, UserID })
        }
        function IsExisit(Parm1, Param2) {
            return $http.post("/a26/IsExisit ", { Parm1, Param2 })
        }
        function a25005(MainObj, Perms, SelectedAg, UnSelectedAg) {
            return $http.post("/a26/a25005 ", { MainObj, Perms, SelectedAg, UnSelectedAg })
        }
        function a25002(UserID, logedInID) {
            return $http.post("/a26/a25002 ", { UserID, logedInID })
        }
        function a25003(mainObj, Perms) {
            return $http.post("/a26/a25003 ", { mainObj, Perms })
        }
        function a25004(IsAll, UserID) {
            return $http.post("/a26/a25004", { IsAll, UserID })
        }
        function a29001(Parm1, Parm2) {
            return $http.post("/a27/a29001", { Parm1, Parm2 })
        }
        function a29002(SessionToken) {
            return $http.post("/a27/a29002", { SessionToken })
        }
        function a29003(SessionToken) {
            return $http.post("/a27/a29003", { SessionToken })
        }
        function a29004(SessionToken) {
            return $http.post("/a27/a29004", { SessionToken })
        }
        function a27015(NewPassword, OldPassword, UserID) {
            return $http.post("/a27/a27015", { NewPassword, OldPassword, UserID })
        }
        function a27016(UserID) {
            return $http.post("/a27/a27016", { UserID })
        }
        function d070001(UserID) {
            return $http.post("/d070/d070001", { UserID });
        }
        function d070002(UserID) {
            return $http.post("/d070/d070002", { UserID });
        }
        function d070003(UserID, MainObj, Li2) {
            return $http.post("/d070/d070003", { UserID, MainObj, Li2 });
        }

        // Agency Equipment Management a59
        function a59001(AgencyID, UserID) {
            return $http.post("/a59/a59001", { AgencyID, UserID });
        }

        function a59002() {
            return $http.post("/a59/a59002");
        }

        function a59003(obj) {
            return $http.post("/a59/a59003", obj);
        }

        function a59004(obj) {
            return $http.post("/a59/a59004", obj);
        }

        function a59005(ID, UserID) {
            return $http.post("/a59/a59005", { ID, UserID });
        }

        function a59006(ID) {
            return $http.post("/a59/a59006", { ID });
        }

        // Stock Requests System - a61
        function a61001(UserID) {
            return $http.post("/a61/a61001", { UserID });
        }

        function a61002(UserID) {
            return $http.post("/a61/a61002", { UserID });
        }

        function a61003(requestObj, UserID) {
            return $http.post("/a61/a61003", { requestObj, UserID });
        }

        function a61004(requestObj, UserID) {
            return $http.post("/a61/a61004", { requestObj, UserID });
        }

        function a61005(RequestID, UserID) {
            return $http.post("/a61/a61005", { RequestID, UserID });
        }

        function a61006(RequestID, Action, Notes, UserID) {
            return $http.post("/a61/a61006", { RequestID, Action, Notes, UserID });
        }

        function a61007(RequestID, UserID) {
            return $http.post("/a61/a61007", { RequestID, UserID });
        }

        function a61008(RequestID, UserID) {
            return $http.post("/a61/a61008", { RequestID, UserID });
        }

        function a61009(RequestID, UserID) {
            return $http.post("/a61/a61009", { RequestID, UserID });
        }

        // Request Management System - a72
        function a72001(page, pageSize, UserID) {
            return $http.post("/a72/a72001", { page, pageSize, UserID });
        }

        function a72003(RequestID, UserID) {
            return $http.post("/a72/a72003", { RequestID, UserID });
        }

        function a72004(RequestID, IsApproved, ReviewNotes, RejectionReason, StartSequence, Quantity, EndSequence, UserID) {
            return $http.post("/a72/a72004", { RequestID, IsApproved, ReviewNotes, RejectionReason, StartSequence, Quantity, EndSequence, UserID });
        }

        function a72005(RequestID, ExecutionNotes, UserID) {
            return $http.post("/a72/a72005", { RequestID, ExecutionNotes, UserID });
        }

        function a72006(UserID) {
            return $http.post("/a72/a72006", { UserID });
        }

        function a72007(SearchTerm, StatusFilter, DateFrom, DateTo, page, pageSize, UserID) {
            return $http.post("/a72/a72007", { SearchTerm, StatusFilter, DateFrom, DateTo, page, pageSize, UserID });
        }

        function a72008(RequestID, IsApproved, ReviewNotes, RejectionReason, StartSequence, Quantity, EndSequence, UserID) {
            return $http.post("/a72/a72008", { RequestID, IsApproved, ReviewNotes, RejectionReason, StartSequence, Quantity, EndSequence, UserID });
        }

        // Contract Termination System - a73 (Categories system removed)
        function a73001(searchTerm, UserID) {
            return $http.post("/a73/a73001", { searchTerm, UserID });
        }

        function a73002(AgencyID, UserID) {
            return $http.post("/a73/a73002", { AgencyID, UserID });
        }

        function a73003(AgencyID, UserID) {
            return $http.post("/a73/a73003", { AgencyID, UserID });
        }

        // Custody Receipt System - a74
        function a74001(agencyId) {
            return $http.get('/a74/GetAgencyById', { params: { agencyId: agencyId } });
        }

        function a74002(agencyId) {
            return $http.get('/a74/a74002', { params: { agencyId: agencyId } });
        }

        function a74003(receiptData) {
            return $http.post('/a74/a74003', receiptData);
        }

        function a74004(receiptId) {
            return $http.get('/a74/a74004', { params: { receiptId: receiptId } });
        }

        // Percentage Management System - d072
        function d072001(UserID) {
            return $http.post("/d072/d072001", { UserID });
        }

        function d072002(model, UserID) {
            return $http.post("/d072/d072002", { model, UserID });
        }

        function d072003(model, UserID) {
            return $http.post("/d072/d072003", { model, UserID });
        }

        function d072004(id, status, UserID) {
            return $http.post("/d072/d072004", { id, status, UserID });
        }

        // Academic Qualification Management System - d073
        function d073001(UserID) {
            return $http.post("/d073/d073001", { UserID });
        }

        function d073002(model, UserID) {
            return $http.post("/d073/d073002", { model, UserID });
        }

        function d073003(model, UserID) {
            return $http.post("/d073/d073003", { model, UserID });
        }

        function d073004(id, status, UserID) {
            return $http.post("/d073/d073004", { id, status, UserID });
        }

        // Main Account Management System - d074
        function d074001(UserID) {
            return $http.post("/d074/d074001", { UserID });
        }

        function d074002(model, UserID) {
            return $http.post("/d074/d074002", { model, UserID });
        }

        function d074003(model, UserID) {
            return $http.post("/d074/d074003", { model, UserID });
        }

        function d074004(id, status, UserID) {
            return $http.post("/d074/d074004", { id, status, UserID });
        }
    }

})();