﻿ (function () {
    'use strict';

    var controllerId = 'a40';
    angular.module('App').controller(controllerId, ["DataService", '$filter', "Notification", "blockUI", "$stateParams", "$state", Func]);

    function Func(DataService, $filter, Notification, blockUI, $stateParams, $state) {
        var vm = this;
        vm.ShowFlag = 0;
        vm.initializeSession = function() {
            var sessionToken = localStorage.getItem('sessionToken');
            var userID = localStorage.getItem('userID');
            var userName = localStorage.getItem('userName');
          
            if (!sessionToken || !userID) {
                $state.go('LoginPage');
                return false;
            }
            
            // Validate session
            DataService.a29002(sessionToken).then(function(response) {
                var data = response.data;
                if (data.ErrorCode === 0) {
                    vm.UserID = data.UserID;
                    vm.UserName = data.UserName;
                    vm.SessionToken = sessionToken;
                   
                    vm.IsliDisFlag = false;
                    vm.successMsg = '';
                    vm.CountryFilter = [];
                    vm.CityFilter = [];
                    vm.DlFalg = 0;
                    a40001();
                    a40003();
                    a40111();
                    a40073();
                    
                   
                } else {
                    // Session invalid, redirect to login
                    vm.clearSessionData();
                    $state.go('LoginPage');
                }
            }, function() {
                // Error validating session, redirect to login
                vm.clearSessionData();
                $state.go('LoginPage');
            });
        };
        vm.initializeSession();
        vm.clearSessionData = function() {
            localStorage.removeItem('sessionToken');
            localStorage.removeItem('jwtToken');
            localStorage.removeItem('userID');
            localStorage.removeItem('userName');
            localStorage.removeItem('compID');
        };
        vm.logout = function() {
            var sessionToken = localStorage.getItem('sessionToken');
            if (sessionToken) {
                DataService.a29003(sessionToken).then(function() {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    Notification.success({ 
                        message: "تم تسجيل الخروج بنجاح", 
                        title: 'تسجيل الخروج' 
                    });
                }, function() {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                });
            } else {
                vm.clearSessionData();
                $state.go('LoginPage');
            }
        };

vm.AgPerObj = {
            Cump_Sys: false,
            Cump_ChangeOwnership: false,
            Cump_AdditionalServices: false,
            Cump_OldInterface: false,
            Orange_Sys: false,
            Travel_Sys: false,
            Trav_Calculation: false,
            Trav_ShowAllDocs: false,
            Med_ResponsibilitySys: false,
            selecteAgencyID: '',
            InsertBy: vm.UserID,
}

        vm.obj = {
            AID: '',
            Aname: '',
            Apass: '',
            ID: '',
            Num: '',
            Name: '',
            NameEn: '',
            RenewCheck: '',
            ReplaceCheck: '',
            CatID: '',
            ProfitMargin: '',
            Del_Date: '',
            AgencyType: '',
            ContryCoID: '',
            IsLocalCurrnecy: '',
            image: '',
            AgFullName: '',
            RptType: '',
            FID: '',
            SID: '',
            ZIPpostCode: '',
            PhoneNum: '',
            Status: '',
            FullName: '',
            AdSearchCheck: '',
            CanExtendCheak: '',
            CanClassDocs: '',
            MID: vm.UserID,
            MID2: vm.UserID,
            InsertDate: new Date(),
            UpdateDate: new Date(),
            Trav_IsADibt: '',
            Trav_debtVal: '',
            MidRes_AllowedDibtValue: '',
            Orang_AllowedDibtValue: '',
            Orang_IsADibt: '',
            MidRes_IsADibt: '',
            LemitDayesPaym: '',
            UnusedDocsLimt: '',
            IntiaiPalance: '',
            IsAllowedDibt: '',
            AgenMainAccID: '',

        };
        vm.obj2 = {
            ID: '',
            Comp_ID: '',
            AgencyID: '',
            UserID: vm.UserID,
        }


        vm.a400059 = function (x) {
            vm.selecteAgencyID = x.AgencyID;
            vm.selectedAgencyName = x.AgencyName;
            a400072();
            vm.ShowFlag = 3;

        }

        function a400072() {
            blockUI.start();
            DataService.a400072(vm.selecteAgencyID, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.AgPerObj = Response.data.AgPer;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    Notification.error({ message: "لا تملك الصلاحيات المناسبة", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        };

        vm.a400071 = function () {
            blockUI.start();
            vm.AgPerObj.selecteAgencyID = vm.selecteAgencyID;
            vm.AgPerObj.InsertBy = vm.UserID;
            DataService.a400071(vm.AgPerObj).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    //a250025()
                    Notification.success({ message: "تمت عملية الاضافة بنجاح", title: 'نجاح العملية' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {

                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        vm.a400030 = function () {
            blockUI.start();
            DataService.a400030(vm.obj2.AgUId, vm.Status, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    a400031();
                    $('#Dl_MessageComp').modal('hide');
                    blockUI.stop();
                    if (vm.Status == 1) {
                        Notification.success({ message: "تمت عملية الحذف بنجاح", title: 'نجاح العملية' });
                    }
                    if (vm.Status == 3) {
                        Notification.success({ message: "تمت عملية التفعيل بنجاح", title: 'نجاح العملية' });
                    }
                    if (vm.Status == 2) {
                        Notification.success({ message: "تمت عملية الايقاف بنجاح", title: 'نجاح العملية' });
                    }
                }
                if (Response.data.ErrorCode == 3) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 4) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }

            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        vm.a400029 = function (x, y) {
            vm.Status = y;
            vm.obj2.AgUId = x.ID;

            vm.DlTitle = "حالة الشركة ";
            if (y == 1)
                vm.DlMessageUs = "هل تريد تفعيل المستخدم  " + x.compName + " ؟";
            if (y == 2)
                vm.DlMessageUs = "هل تريد إيقاف المستخدم  " + x.compName + " ؟";
            if (y == 3)
                vm.DlMessageUs = "هل تريد حذف  المستخدم  " + x.compName + " ؟";
            $('#Dl_MessageComp').modal('show');
        }
        function GetListoFCO() {
            blockUI.start();
            DataService.a400032(vm.obj2.AgencyID, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    vm.ListCO = Response.data.CompLi;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: + Response.data.ErrorMessage, title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.a4000027 = function () {
            blockUI.start();
            DataService.a4000027(vm.obj2).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    a400021();
                    $('#NewUser').modal('hide');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 3) {
                    Notification.error({ message: "خطأ في عملية الحفظ ", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.a400028 = function (x) {
            vm.DlTitle = " تعديل  الشركة";
            vm.obj2.ID = x.ID;
            vm.obj2.AgencyID = x.AgencyID;
            vm.obj2.Comp_ID = x.Comp_ID;
            vm.DlFalg = 1;
            $('#NewComp').modal('show');
        }
        vm.a4000026 = function () {
            blockUI.start();
            DataService.a4000026(vm.obj2.AgUName, vm.obj2.AgUId, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    vm.IsExitUs = Response.data.IsExitUs;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.a4000025 = function () {
            blockUI.start();
            DataService.a400033(vm.obj2.AgencyID, vm.Comp_ID, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    a400031();
                    $('#NewUser').modal('hide');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 3) {
                    Notification.error({ message: "خطأ في عملية الحفظ ", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.a400022 = function () {
            GetListoFCO();
            vm.obj.ID = '';
            vm.obj.Aname = '';
            vm.obj.Apass = '';
            vm.IsExitUs = false;
            vm.DlTitle = " اضافة شركة جديدة   ";
            vm.DlFalg = 0;

            $('#NewUser').modal('show');
        }

        function a400031() {
            blockUI.start();
            DataService.a400031(vm.obj2.AgencyID, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    vm.companies = Response.data.agCompTb;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: + Response.data.ErrorMessage, title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.a400020 = function (x) {
            vm.ShowFlag = 1;
            vm.SelectedAgName = x.AgencyName;
            vm.obj2.AgencyID = x.AgencyID
            a400031();
        }

        vm.BtnBack = function () {
            vm.ShowFlag = 0;
        }
        vm.li_Selected = function () {
            li_sil();
        }
        function li_sil() {
            if (vm.obj.AgencyType == 1 || vm.obj.CatID == '43C5C47B-629C-4256-BCE2-4B767F4EBED2') {
                vm.obj.FID = vm.li;
                vm.IsliDisFlag = true
                vm.obj.IsLocalCurrnecy = '0';
                a40040(0);
            }
            else {
                vm.obj.FID = "";
                vm.IsliDisFlag = false
                vm.obj.IsLocalCurrnecy = '1';
            }

        }
        vm.a40008 = function () {
            blockUI.start();
            DataService.a40008(vm.obj, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    vm.AgencyList = Response.data.AgencyList;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.AgnTypeChanged = function () {
            a40010();

        }
        function a40111() {
            blockUI.start();
            DataService.a40010(vm.UserID, 1).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.AgenCatsFilter = Response.data.AgenCats;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 2) {
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        function a40010(a) {
            blockUI.start();
            DataService.a40010(vm.UserID, vm.obj.AgencyType).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.AgenCats = Response.data.AgenCats;
                    vm.obj.CatID = a;
                    li_sil();
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 2) {
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.a40007 = function () {
            blockUI.start();
            DataService.a40007(vm.obj, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    Notification.error({ message: "الرجاء تغيير إسم  مركز الإصدار", title: 'الإسم موجد مسبقا' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 2) {
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        vm.Stop = function () {
            blockUI.start();
            if (vm.UserID == '') {
                blockUI.stop();
                $state.go('LoginPage');
            }
            DataService.a40009(vm.obj, vm.Status, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    Notification.success({ message: "تمت عملية الحذف بنجاح", title: 'نجاح العملية' });
                    a40001();
                    $('#Dl_Message').modal('hide');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.success({ message: "تمت عملية التفعيل بنجاح", title: 'نجاح العملية' });
                    a40001();
                    $('#Dl_Message').modal('hide');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 2) {
                    Notification.success({ message: "تمت عملية الايقاف بنجاح", title: 'نجاح العملية' });
                    a40001();
                    $('#Dl_Message').modal('hide');
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 4) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }

            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        vm.BtnStop = function (x, y) {
            vm.Status = y;
            vm.obj.ID = x.AgencyID;
            vm.AgencyName = x.AgencyName;
            vm.DlTitle = "حالة مركز الإصدار";
            if (y == 1)
                vm.DlMessage = "هل تريد تفعيل مركز الإصدار  " + x.AgencyName + " ؟";
            if (y == 2)
                vm.DlMessage = "هل تريد إيقاف مركز الإصدار  " + x.AgencyName + " ؟";
            if (y == 3)
                vm.DlMessage = "هل تريد حذف  مركز الإصدار  " + x.AgencyName + " ؟";
            $('#Dl_Message').modal('show');
        }

        vm.BtnDelete = function BtnDelete(x) {
            vm.title = "حذف بيانات  مركز الإصدار";
            vm.obj.ID = x.AgencyID;
            vm.successMsg = " هل تريد حدف " + x.AgencyName + " ؟";
            vm.DlTitle = "حذف الوكيل";
            $('#Dl_Message').modal('show');

        };
        vm.Edit = function () {
            blockUI.start();
            if (vm.UserID == '') {
                blockUI.stop();
                $state.go('LoginPage');
            }
            DataService.a40005(vm.obj, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    Notification.success({ message: "تمت عملية التعديل بنجاح", title: 'نجاح العملية' });
                    a40001();
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    vm.ErrorCode = Response.data.ErrorCode;
                    vm.InsErrorMess = "تم إضافة المستخدم مسبقا";
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 6) {
                    vm.ErrorCode = Response.data.ErrorCode;
                    vm.InsErrorMessNum = "الرقم مكرر";
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        vm.BtnMainEdit = function (x) {
            vm.ErrorCode = '';
            vm.obj.ID = x.AgencyID;
            vm.obj.Name = x.AgencyName;
            vm.obj.NameEn = x.NameEn;
            vm.obj.FID = x.ContryID.toString();
            vm.obj.SID = x.CityID;
            vm.obj.IntiaiPalance = x.IntiaiPalance;
            vm.obj.UnusedDocsLimt = x.UnusedDocsLimt;
            vm.obj.LemitDayesPaym = x.LemitDayesPaym;
            vm.obj.Orang_AllowedDibtValue = x.Orang_AllowedDibtValue;
            vm.obj.MidRes_AllowedDibtValue = x.MidRes_AllowedDibtValue;
            vm.obj.MidRes_IsADibt = x.MidRes_IsADibt == false ? '0' : '1';
            vm.obj.Orang_IsADibt = x.Orang_IsADibt == false ? '0' : '1';
            vm.ImageFile = null;
            a40040();
            vm.obj.PhoneNum = x.PhoneNum;
            vm.obj.AgencyType = x.agType.toString();
            vm.obj.IsLocalCurrnecy = x.IsLocalCurrnecy.toString();
            a40010(x.AgenCatID.toString());
            vm.obj.Aname = x.AgUserName;
            vm.obj.Apass = x.Passowrd;
            vm.obj.Trav_IsADibt = x.Trav_IsADibt;
            vm.obj.Trav_debtVal = x.Trav_debtVal;
            vm.obj.IsADibt = x.IsAllowedDibt == false ? '0' : '1';
            vm.obj.debtVal = x.AllowedDibtValue;
            vm.obj.AgUserID = x.AgUserID;
            vm.obj.AgFullName = x.FullName;
            vm.obj.ContryCoID = x.Agnum;
            vm.obj.AgenMainAccID = x.AgenMainAccID;
            vm.obj.ProfitMargin = x.ProftMargin;
            vm.DlTitle = "تعديل بيانات  مركز الإصدار";
            vm.DlFalg = 1;
            vm.InsErrorMessNum = 0;
            vm.InsErrorMess = 0;
            /*GetImg(vm.obj.ID);*/

            $('#NewItem').modal('show');

        }
        vm.a40004 = function () {
            a40040();
            if (vm.obj.FID == vm.li)
                vm.obj.IsLocalCurrnecy = '0';
            else
                vm.obj.IsLocalCurrnecy = '1';
        }
        function a40040() {
            blockUI.start();
            var x = vm.obj.SID;
            DataService.a40004(vm.obj.FID).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    vm.CityFilter = Response.data.CityFilter;
                    vm.obj.SID = x;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
            });
        }
        //function GetImg(ID) {
        //    blockUI.start();
        //    DataService.a40011(ID, vm.UserID ).then(function (Response) {
        //        if (Response.data.ErrorCode == 0) {
        //            vm.obj.image = Response.data.Img == null ? BlankImg : Response.data.Img;
        //            $('#NewItem').modal('show');
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 1) {
        //            Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 5) {
        //            vm.ErrorCode = Response.data.ErrorCode;
        //            vm.InsErrorMess = "تم إضافة المستخدم مسبقا";
        //            blockUI.stop();
        //        }
        //        if (Response.data.ErrorCode == 3) {
        //            $state.go('LoginPage');
        //            blockUI.stop();
        //        }

        //    }, function (error) {
        //        Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
        //        blockUI.stop();
        //    });
        //}

        function a40073() {
            blockUI.start();
            DataService.a40073(vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.MainAccList = Response.data.MainAccList;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: Response.data.ErrorMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    Notification.error({ message: "لا تملك الصلاحيات المناسبة", title: 'خطأ' });
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }


        vm.Save = function () {
            blockUI.start();
            if (vm.UserID == '') {
                blockUI.stop();
                $state.go('LoginPage');
            }
            DataService.a40002(vm.obj, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    Notification.success({ message: "تمت عملية الإضافة بنجاح", title: 'نجاح العملية' });
                    a40001();

                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    vm.ErrorCode = Response.data.ErrorCode;
                    vm.InsErrorMess = "تم إضافة المستخدم مسبقا";
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }

            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        function NewForm() {
            vm.obj.Name = '';
            vm.obj.NameEn = '';
            vm.obj.FID = '';
            vm.obj.SID = '';
            vm.obj.AgencyType = '';
            vm.obj.ZIPpostCode = '';
            vm.obj.PhoneNum = '';
            vm.obj.AgFullName = '';
            vm.obj.Orang_IsADibt = '';
            vm.obj.MidRes_IsADibt = '';
            vm.obj.MidRes_AllowedDibtValue = '';
            vm.obj.Orang_AllowedDibtValue = '';
            vm.obj.UnusedDocsLimt = '';
            vm.obj.LemitDayesPaym = '';
            vm.obj.IntiaiPalance = '';
            vm.obj.CatID = '';
            vm.obj.Aname = '';
            vm.obj.Apass = '';
            vm.obj.RptType = '';
            vm.ImageFile = null;
            vm.obj.AgUserID = '';
            vm.obj.AgenMainAccID = '';
            vm.obj.ProfitMargin = '';
            vm.obj.image = "data:image/jpeg;base64,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";
            vm.obj.IsLocalCurrnecy = '';
            vm.Aname = '';
            vm.Apass = '';
            vm.DlTitle = "إضافة  مركز الإصدار";
            vm.DlFalg = 0;
            vm.InsErrorMessNum = 0;
            vm.InsErrorMess = 0;
            vm.ErrorCode = '';
        }
        vm.AddNew = function () {
            NewForm();
        }
        function a40001() {
            blockUI.start();
            DataService.a40001(vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    vm.AgencyList = Response.data.AgencyList;
                    vm.li = Response.data.li;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            });
        }



        function a40003() {
            blockUI.start();
            DataService.a40003().then(function (Response) {
                if (Response.data.ErrorCode === 0) {
                    vm.CountryFilter = Response.data.CountryFilter;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode === 1) {
                    Notification.error({ message: Response.data.Message + "<br><br> (Error No: " + Response.data.ErrorMessage + ")", title: 'Error' });
                    blockUI.stop();
                }
            });
        }



        vm.BtnHomePage = function () {
            $state.go('HomePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'ShowFlag': 3 })
        }
        vm.sys_page = function () {
            $state.go('HomePage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'ShowFlag': 0 })
        }
        vm.BtnAgencyPage = function () {
            $state.go('AgencyPage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'ShowFlag': 0 })
        }

        // Navigate to Agency Equipment page for specific agency
        vm.a400080 = function (x) {
            $state.go('AgencyEquipmentPage', { 'UserID': vm.UserID, 'UserName': vm.UserName, 'AgencyID': x.AgencyID, 'AgencyName': x.AgencyName })
        }
        vm.btnBack = function () {
            vm.switchFlag = 1;

        }

    }
})();

