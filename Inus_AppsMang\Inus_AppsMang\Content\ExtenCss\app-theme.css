/* General Body Styles */
body {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl;
    text-align: right;
    /* Modern dark background for professional admin app */
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #1a1a2e 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Animated gradient background */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Add a subtle dark overlay pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
        linear-gradient(45deg, rgba(255, 255, 255, 0.01) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(255, 255, 255, 0.01) 25%, transparent 25%);
    background-size: 200px 200px, 300px 300px, 50px 50px, 50px 50px;
    background-position: 0 0, 100px 100px, 0 0, 25px 25px;
    animation: patternMove 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes patternMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(50px) translateY(50px); }
}

/* Add subtle floating elements for modern dark feel */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(83, 52, 131, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(15, 52, 96, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(22, 33, 62, 0.06) 0%, transparent 50%);
    animation: floatingShapes 25s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes floatingShapes {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

/* Font Family Consistency */
* {
    font-family: 'Cairo', sans-serif !important;
}

/* Form Controls RTL Support */
.form-control, .form-select, .btn, .table {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl;
}

.table th, .table td {
    text-align: right;
}

/* Enhanced Card Styles */
.card, .modal-content, .dropdown-menu {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Enhanced Navbar/Header Styles */
.navbar, .bg-primary, .bg-secondary {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Text Colors */
.text-dark {
    color: #ffffff !important;
}

.text-light, .text-white {
    color: #ffffff !important;
}

/* Enhanced Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #533483, #0f3460);
    border: none;
    box-shadow: 0 4px 15px rgba(83, 52, 131, 0.4);
    color: #ffffff;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0f3460, #533483);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(83, 52, 131, 0.5);
    color: #ffffff;
}

/* Custom Dropdown Styles */
.dropdown-menu {
    display: none !important;
    position: absolute !important;
    z-index: 1050 !important;
    min-width: 160px;
    padding: 5px 0;
    margin: 0 !important;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.dropdown-menu.show {
    display: block !important;
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
    text-decoration: none;
    background-color: transparent;
    border: 0;
    cursor: pointer;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #262626;
    text-decoration: none;
    background-color: #f5f5f5;
}

.dropdown {
    position: relative;
}

.dropdown-menu[dir="ltr"] {
    text-align: left;
} 