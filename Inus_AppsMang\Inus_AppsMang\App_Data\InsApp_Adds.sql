﻿alter table [dbo].[Agency]
add 
 SoldMidRes float,
 LemitDayesPaym int,
 IntiaiPalance float,
 UsedValue float,
 AddValue float,
 Plance float,
 UnusedDocsLimt int,
 MidRes_AllowedDibtValue float,
 Orang_AllowedDibtValue float,
 MidRes_IsADibt bit ,
 Orang_IsADibt bit 


 alter table [dbo].[Agency] 
 drop COLUMN Comp_tot,Trav_total, HasReplaceDoc , HasRenewDoc ,HasAdvanceSearch ,HasExtendDocDate ,HasClassDocPer ,RPT_Type

   

 CREATE TABLE [dbo].[AgenSysProfit](
	[ID] [bigint] NOT NULL,
	[AgencyID] [uniqueidentifier] NULL,
	[SysID] [uniqueidentifier] NULL,
	[Val] [float] NULL,
	[IsPercenge] [bit] NULL,
	[InsertBy] [uniqueidentifier] NULL,
	[Status] [tinyint] NULL,
	[InsertDate] [datetime] NULL,
	[updateDate] [datetime] NULL,
	[UpdateBY] [uniqueidentifier] NULL,
 CONSTRAINT [PK_AgenSysProfit] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[AgenSysProfit]  WITH CHECK ADD  CONSTRAINT [FK_AgenSysProfit_Agency] FOREIGN KEY([AgencyID])
REFERENCES [dbo].[Agency] ([AgencyID])
GO

ALTER TABLE [dbo].[AgenSysProfit] CHECK CONSTRAINT [FK_AgenSysProfit_Agency]
GO

ALTER TABLE [dbo].[AgenSysProfit]  WITH CHECK ADD  CONSTRAINT [FK_AgenSysProfit_Systems] FOREIGN KEY([SysID])
REFERENCES [dbo].[Systems] ([Sys_ID])
GO

ALTER TABLE [dbo].[AgenSysProfit] CHECK CONSTRAINT [FK_AgenSysProfit_Systems]
GO


CREATE TABLE [dbo].[monthlyAgenFinnce](
	[ID] [int] NOT NULL,
	[Month] [int] NULL,
	[Year] [int] NULL,
	[AgencyID] [uniqueidentifier] NULL,
	[ToatalOrange] [float] NULL,
	[TotalComp] [float] NULL,
	[TotalMidres] [float] NULL,
	[TotalTrav] [float] NULL,
	[NumOfDocs_Orange] [int] NULL,
	[NumOfDeletedDocs_Orange] [int] NULL,
	[NumOfvaidDocs_Orange] [int] NULL,
	[NumOfDocs_Trav] [int] NULL,
	[NumOfDocs_Midres] [int] NULL,
	[NumOfDocs_Comp] [int] NULL,
	[NumOfDeletedDocs_Trav] [int] NULL,
	[NumOfDeletedDocs_Midres] [int] NULL,
	[NumOfDeletedDocs_Comp] [int] NULL,
	[NumOfvaidDocs_Midres] [int] NULL,
	[NumOfvaidDocs_Trav] [int] NULL,
	[NumOfvaidDocs__Comp] [int] NULL,
	[MonthTotal] [float] NULL,
	[Profit] [float] NULL,
	[ProfitVal] [float] NULL,
	[Dibt] [float] NULL,
	[Profit_Ispercntage] [bit] NULL,
	[ProfitTotal] [float] NULL,
	[Status] [tinyint] NULL,
	[IsAdopt] [bit] NULL,
	[IsCashedIn] [bit] NULL,
	[IsAgenCashed] [bit] NULL,
	[InCashedDate] [datetime] NULL,
	[IncashedBy] [uniqueidentifier] NULL,
	[AgenCashedBy] [uniqueidentifier] NULL,
	[AgenCashedDate] [datetime] NULL,
 CONSTRAINT [PK_monthlyAgenFinnce] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO


CREATE TABLE EquipmentCategories (
    CategoryID int PRIMARY KEY IDENTITY(1,1),
    CategoryName nvarchar(50) NOT NULL,
    Description nvarchar(200) NULL,
    IsActive bit DEFAULT 1
)

-- Insert default categories
INSERT INTO EquipmentCategories (CategoryName, Description) VALUES 
('Computers', 'Desktop PCs, Laptops, Tablets'),
('Printers', 'Laser, Inkjet, Multifunction printers'),
('Office Supplies', 'Paper, Ink, Stationery'),
('Furniture', 'Desks, Chairs, Cabinets'),
('Communication', 'Phones, Fax machines')

-- Create Main Equipment Table
CREATE TABLE AgencyEquipment (
    ID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    AgencyID uniqueidentifier NOT NULL,
    CategoryID int NOT NULL,
    ItemName nvarchar(100) NOT NULL,
    Brand nvarchar(50) NULL,
    Model nvarchar(50) NULL,
    SerialNumber nvarchar(50) NULL,
    Quantity int NOT NULL DEFAULT 1,
    UnitType nvarchar(20) NULL,
    Status tinyint NOT NULL DEFAULT 1, -- 1=Working, 2=Maintenance, 3=Damaged, 4=Disposed, 5=Out of Stock
    Notes nvarchar(500) NULL,
    InsertedBy uniqueidentifier NULL,
    InsertedDate datetime NULL DEFAULT GETDATE(),
    UpdatedBy uniqueidentifier NULL,
    UpdatedDate datetime NULL,
    
    CONSTRAINT FK_AgencyEquipment_Agency 
        FOREIGN KEY (AgencyID) REFERENCES Agency(AgencyID),
    CONSTRAINT FK_AgencyEquipment_Category 
        FOREIGN KEY (CategoryID) REFERENCES EquipmentCategories(CategoryID)
)

-- Create indexes for better performance
CREATE INDEX IX_AgencyEquipment_AgencyID ON AgencyEquipment(AgencyID)
CREATE INDEX IX_AgencyEquipment_CategoryID ON AgencyEquipment(CategoryID)
CREATE INDEX IX_AgencyEquipment_Status ON AgencyEquipment(Status)

-- Insert sample data (optional - remove if not needed)
-- DECLARE @SampleAgencyID uniqueidentifier = (SELECT TOP 1 AgencyID FROM Agency)
-- IF @SampleAgencyID IS NOT NULL
-- BEGIN
--     INSERT INTO AgencyEquipment (AgencyID, CategoryID, ItemName, Brand, Model, SerialNumber, Quantity, UnitType, Status, Notes) VALUES
--     (@SampleAgencyID, 1, 'Desktop PC - Dell OptiPlex', 'Dell', 'OptiPlex 3090', 'DL001234', 1, 'Units', 1, 'Main office computer'),
--     (@SampleAgencyID, 2, 'Laser Printer HP', 'HP', 'LaserJet Pro M404n', 'HP567890', 1, 'Units', 1, 'Reception area printer'),
--     (@SampleAgencyID, 3, 'A4 Paper - White', 'Double A', 'Premium', NULL, 50, 'Reams', 1, 'White 80gsm paper for printing')
-- END

PRINT 'Agency Equipment tables created successfully!' 

-- =============================================
-- Orange Insurance Inventory/Stock Management Tables (مخزون تأمين البرتقالية)
-- Created: 2024
-- Description: Database tables for Orange Insurance inventory/stock management system
-- Based on Medical Responsibility Insurance structure with Oran_ prefix
-- =============================================

-- =============================================
-- SERIAL NUMBER MANAGEMENT TABLES FOR INVENTORY
-- =============================================

-- 1. Create Orange Serial Status Table
CREATE TABLE Oran_SearStatus (
    StatusID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    StatusName nvarchar(50) NULL,
    StatusDescription nvarchar(200) NULL,
    Status tinyint NULL DEFAULT 1, -- 1=Active, 2=Inactive, 3=Deleted
    InsertedBy uniqueidentifier NULL,
    InsertDate datetime NULL DEFAULT GETDATE(),
    UpdateDate datetime NULL,
    UpdatedBy uniqueidentifier NULL
)

-- 2. Create Orange Serials Main Table
CREATE TABLE Oran_Seariles (
    Sea_ID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    AgencyID uniqueidentifier NULL,
    Serale_Start int NULL,
    Serale_End int NULL,
    Agntype nvarchar(50) NULL,
    Status tinyint NULL DEFAULT 1, -- 1=Active, 2=Inactive, 3=Deleted
    InsertedBy uniqueidentifier NULL,
    InsertDate datetime NULL DEFAULT GETDATE(),
    UpdateDate datetime NULL,
    UpdatedBy uniqueidentifier NULL,
    
    CONSTRAINT FK_Oran_Seariles_Agency 
        FOREIGN KEY (AgencyID) REFERENCES Agency(AgencyID)
)

-- 3. Create Orange Given Serials Table
CREATE TABLE Oran_GivenSearils (
    SerialsGivID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    SrID uniqueidentifier NULL,
    AgencyID uniqueidentifier NULL,
    Start int NULL,
    SerEnd int NULL,
    UsedQty int NULL DEFAULT 0,
    Status tinyint NULL DEFAULT 1, -- 1=Active, 2=Inactive, 3=Deleted
    InsertedBy uniqueidentifier NULL,
    InsertDate datetime NULL DEFAULT GETDATE(),
    UpdateDate datetime NULL,
    UpdatedBy uniqueidentifier NULL,
    
    CONSTRAINT FK_Oran_GivenSearils_Seariles 
        FOREIGN KEY (SrID) REFERENCES Oran_Seariles(Sea_ID),
    CONSTRAINT FK_Oran_GivenSearils_Agency 
        FOREIGN KEY (AgencyID) REFERENCES Agency(AgencyID)
)

-- 4. Create Orange Serial Numbers Table
CREATE TABLE Oran_SerialNums (
    SerialID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    SerialNumber int NULL,
    SearilsID uniqueidentifier NULL,
    StatusID uniqueidentifier NULL,
    AgUserID uniqueidentifier NULL,
    AgencyID uniqueidentifier NULL,
    SerGivID uniqueidentifier NULL,
    Status tinyint NULL DEFAULT 1, -- 1=Available, 2=Used, 3=Cancelled, 4=Reserved
    InsertedBy uniqueidentifier NULL,
    InsertDate datetime NULL DEFAULT GETDATE(),
    UpdateDate datetime NULL,
    UpdatedBy uniqueidentifier NULL,
    
    CONSTRAINT FK_Oran_SerialNums_Seariles 
        FOREIGN KEY (SearilsID) REFERENCES Oran_Seariles(Sea_ID),
    CONSTRAINT FK_Oran_SerialNums_SearStatus 
        FOREIGN KEY (StatusID) REFERENCES Oran_SearStatus(StatusID),
    CONSTRAINT FK_Oran_SerialNums_AgUsers 
        FOREIGN KEY (AgUserID) REFERENCES AgUsers(AgUserID),
    CONSTRAINT FK_Oran_SerialNums_Agency 
        FOREIGN KEY (AgencyID) REFERENCES Agency(AgencyID),
    CONSTRAINT FK_Oran_SerialNums_GivenSearils 
        FOREIGN KEY (SerGivID) REFERENCES Oran_GivenSearils(SerialsGivID)
)

-- 5. Create Orange Users Serials Table
CREATE TABLE Oran_UsersSearils (
    UserSerilsID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    AgUserID uniqueidentifier NULL,
    AgencyID uniqueidentifier NULL,
    SerGivID uniqueidentifier NULL,
    StartSerial int NULL,
    EndSerial int NULL,
    Quantity int NULL DEFAULT 0,
    UsedQuantity int NULL DEFAULT 0,
    Status tinyint NULL DEFAULT 1, -- 1=Active, 2=Completed, 3=Cancelled
    InsertedBy uniqueidentifier NULL,
    InsertDate datetime NULL DEFAULT GETDATE(),
    UpdateDate datetime NULL,
    UpdatedBy uniqueidentifier NULL,
    
    CONSTRAINT FK_Oran_UsersSearils_AgUsers 
        FOREIGN KEY (AgUserID) REFERENCES AgUsers(AgUserID),
    CONSTRAINT FK_Oran_UsersSearils_Agency 
        FOREIGN KEY (AgencyID) REFERENCES Agency(AgencyID),
    CONSTRAINT FK_Oran_UsersSearils_GivenSearils 
        FOREIGN KEY (SerGivID) REFERENCES Oran_GivenSearils(SerialsGivID)
)

-- 6. Create Orange Printed Serials Table
CREATE TABLE Oran_PrintedSerals (
    PrintedID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    SerialNumber int NULL,
    AgUserID uniqueidentifier NULL,
    AgencyID uniqueidentifier NULL,
    PrintedDate datetime NULL DEFAULT GETDATE(),
    Status tinyint NULL DEFAULT 1, -- 1=Printed Successfully, 2=Print Failed, 3=Reprinted
    InsertedBy uniqueidentifier NULL,
    InsertDate datetime NULL DEFAULT GETDATE(),
    UpdateDate datetime NULL,
    UpdatedBy uniqueidentifier NULL,
    
    CONSTRAINT FK_Oran_PrintedSerals_AgUsers 
        FOREIGN KEY (AgUserID) REFERENCES AgUsers(AgUserID),
    CONSTRAINT FK_Oran_PrintedSerals_Agency 
        FOREIGN KEY (AgencyID) REFERENCES Agency(AgencyID)
)

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Oran_SearStatus Indexes
CREATE INDEX IX_Oran_SearStatus_Status ON Oran_SearStatus(Status)
CREATE INDEX IX_Oran_SearStatus_StatusName ON Oran_SearStatus(StatusName)

-- Oran_Seariles Indexes
CREATE INDEX IX_Oran_Seariles_AgencyID ON Oran_Seariles(AgencyID)
CREATE INDEX IX_Oran_Seariles_Status ON Oran_Seariles(Status)
CREATE INDEX IX_Oran_Seariles_Agntype ON Oran_Seariles(Agntype)

-- Oran_GivenSearils Indexes
CREATE INDEX IX_Oran_GivenSearils_AgencyID ON Oran_GivenSearils(AgencyID)
CREATE INDEX IX_Oran_GivenSearils_SrID ON Oran_GivenSearils(SrID)
CREATE INDEX IX_Oran_GivenSearils_Status ON Oran_GivenSearils(Status)

-- Oran_SerialNums Indexes
CREATE INDEX IX_Oran_SerialNums_AgencyID ON Oran_SerialNums(AgencyID)
CREATE INDEX IX_Oran_SerialNums_Status ON Oran_SerialNums(Status)
CREATE INDEX IX_Oran_SerialNums_SerialNumber ON Oran_SerialNums(SerialNumber)
CREATE INDEX IX_Oran_SerialNums_AgUserID ON Oran_SerialNums(AgUserID)
CREATE INDEX IX_Oran_SerialNums_SearilsID ON Oran_SerialNums(SearilsID)

-- Oran_UsersSearils Indexes
CREATE INDEX IX_Oran_UsersSearils_AgUserID ON Oran_UsersSearils(AgUserID)
CREATE INDEX IX_Oran_UsersSearils_AgencyID ON Oran_UsersSearils(AgencyID)
CREATE INDEX IX_Oran_UsersSearils_Status ON Oran_UsersSearils(Status)
CREATE INDEX IX_Oran_UsersSearils_SerGivID ON Oran_UsersSearils(SerGivID)

-- Oran_PrintedSerals Indexes
CREATE INDEX IX_Oran_PrintedSerals_AgencyID ON Oran_PrintedSerals(AgencyID)
CREATE INDEX IX_Oran_PrintedSerals_AgUserID ON Oran_PrintedSerals(AgUserID)
CREATE INDEX IX_Oran_PrintedSerals_PrintedDate ON Oran_PrintedSerals(PrintedDate)
CREATE INDEX IX_Oran_PrintedSerals_SerialNumber ON Oran_PrintedSerals(SerialNumber)
CREATE INDEX IX_Oran_PrintedSerals_Status ON Oran_PrintedSerals(Status)

-- =============================================
-- INSERT DEFAULT DATA
-- =============================================

-- Insert default Serial Status values
INSERT INTO Oran_SearStatus (StatusID, StatusName, StatusDescription, Status) VALUES 
(NEWID(), 'متاح', 'رقم متاح للاستخدام', 1),
(NEWID(), 'مستخدم', 'رقم مستخدم', 1),
(NEWID(), 'ملغي', 'رقم ملغي', 1),
(NEWID(), 'محجوز', 'رقم محجوز للاستخدام', 1),
(NEWID(), 'معطل', 'رقم معطل مؤقتاً', 2)

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

PRINT '============================================='
PRINT 'Orange Insurance Inventory (مخزون) Tables Created Successfully!'
PRINT ''
PRINT 'Tables Created:'
PRINT '1. Oran_SearStatus (حالة المسلسلات)'
PRINT '2. Oran_Seariles (المسلسلات الرئيسية)'
PRINT '3. Oran_GivenSearils (المسلسلات الممنوحة)'
PRINT '4. Oran_SerialNums (أرقام المسلسلات)'
PRINT '5. Oran_UsersSearils (مسلسلات المستخدمين)'
PRINT '6. Oran_PrintedSerals (المسلسلات المطبوعة)'
PRINT ''
PRINT 'All indexes and foreign key constraints created.'
PRINT 'Default serial status data inserted.'
PRINT '=============================================' 