﻿//const { Button } = require("bootstrap");

(function () {
    'use strict';

    var controllerId = 'a27';
    angular.module('App').controller(controllerId, ["DataService", '$filter', "Notification", "blockUI", "$stateParams", "$state", "$scope", Func]);

    function Func(DataService, $filter, Notification, blockUI, $stateParams, $state, $scope) {
        var vm = this;
        vm.ShowFlag = 0;
        
        // Initialize permissions with default values to prevent ng-if issues
        vm.Perms = {
            agency: false,
            palance: false,
            users: false,
            news: false,
            settings: false,
            InsuranceInterfacemanagmentSys: false,
            agency_sys: false,
            finance_sys: false,
            compolsy_Sys: false,
            traviles_Sys: false,
            orang_sys: false,
            medical_sys: false,
            requests_sys: false,
            med_percentage: false,
            stock_requests_management: false
        };
        
        // SECURITY: Manual session validation only - no auto-login
        // Old initializeSession function removed - now using direct permission loading
        
        // Clear session data
        vm.clearSessionData = function() {
            localStorage.removeItem('sessionToken');
            localStorage.removeItem('jwtToken');
            localStorage.removeItem('userID');
            localStorage.removeItem('userName');
            localStorage.removeItem('compID');
        };
        
        // Logout function
        vm.logout = function () {
            blockUI.start();
            var sessionToken = localStorage.getItem('sessionToken');
            if (sessionToken) {
                DataService.a29003(sessionToken).then(function() {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    Notification.success({ 
                        message: "تم تسجيل الخروج بنجاح", 
                        title: 'تسجيل الخروج' 
                    });
                    blockUI.stop();
                }, function() {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    blockUI.stop();
                });
            } else {
                vm.clearSessionData();
                $state.go('LoginPage');
                blockUI.stop();
            }
        };
        

        
        vm.a27056 = function () {
            if (!vm.SessionToken) {
                $state.go('LoginPage');
                return;
            }
            
            blockUI.start();
            vm.SettingObj.InsertBy = vm.UserID
            DataService.a27056(vm.SettingObj).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    Notification.success({ message: "تمت العملية الحفظ بنجاح", title: 'تعديل كلمة المرور ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        
        function a27054() {
            if (!vm.SessionToken) {
                $state.go('LoginPage');
                return;
            }
            
            blockUI.start();
            DataService.a27054(vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.SettingObj = Response.data.SettingObj;
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        };
        
        vm.Sittings = function () {
            a27054();
            $('#Sittings').modal('show');
        }
        
        vm.a270055 = function () {
            vm.ShowFlag = 10;
        }
        
        vm.SettingObj = {
            SysName: '',
            Settings:'',
        }
        
        vm.Setting = {
            Descrption: '',
            Val: '',
        }
        
        vm.MainObj = {
            AgCount:'',
            UsCount: '',
            CoCount: '',
            ToCount:'',
        };
        
        // Original a27016 function - kept for compatibility
        function a27016() {
            loadMainPermissions();
        } 
        
        vm.a27015 = function () {
            if (!vm.SessionToken) {
                $state.go('LoginPage');
                return;
            }
            
            blockUI.start();
            DataService.a27015(vm.NewPassword, vm.OldPassword, vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    Notification.success({ message: "تمت العملية التعديل بنجاح", title: 'تعديل كلمة المرور ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 1) {
                    Notification.error({ message: "حدث خطأ الرجاء مخاطبة قسم الدعم", title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 5) {
                    Notification.error({ message: Response.data.ErorrMessage, title: 'خطأ' });
                    blockUI.stop();
                }
                if (Response.data.ErrorCode == 3) {
                    vm.clearSessionData();
                    $state.go('LoginPage');
                    blockUI.stop();
                }
            }, function (error) {
                Notification.error({ message: "لايمكن الوصول إلى الخادم", title: 'خطأ' });
                blockUI.stop();
            });
        }
        
        vm.a270059 = function () {
            vm.ShowFlag = 3;
        }
        vm.a270060 = function () {
            vm.ShowFlag = 6;
        }
        
        // نظام إدارة طلبات المخزون
        vm.RequestsManagementPage = function () {
            window.location.href = '/a72/a72000';
        }
    
        // Original a26011 function - kept for compatibility  
        function a26011() {
            loadPermissionsDirectly();
        }
        
        vm.BtnAprrovDel = function () {
            $state.go('DeletionApprovalPage', { "ShowFlag": vm.ShowFlag })
        }

        vm.BtnHomePage = function () {
            $state.go('Com_HomePage');
        }

        vm.BtnTravHomePage = function () {
            $state.go('Tarv_HomePage');
        }

        vm.BtnOrangeHomePage = function () {
            $state.go('Orange_HomePage');
        }

        vm.BtnMedicalHomePage = function () {
            $state.go('Medical_HomePage');
        }

        vm.BtnEthaHomePage = function () {
            $state.go('Etha_HomePage');
        }

        vm.BtnWekaHomePage = function () {
            $state.go('Weka_HomePage');
        }

        vm.BtnAlasimaHomePage = function () {
            $state.go('Alasima_HomePage');
        }

        // Add missing navigation functions that the home page calls
        vm.Com_HomePage = function () {
            $state.go('Com_HomePage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.Tarv_HomePage = function () {
            $state.go('Tarv_HomePage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.OrnagePage = function () {
            $state.go('Orange_HomePage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.Medical_HomePage = function () {
            $state.go('Medical_HomePage', { UserID: vm.UserID, UserName: vm.UserName });
        }

        // إضافة دالة الانتقال إلى صفحة إدارة النسب
        vm.PercentagePage = function () {
            $state.go('PercentagePage', {
                'UserID': vm.UserID,
                'UserName': vm.UserName
            });
        };

        vm.UpdatePasswordDl = function () {
            vm.DlTitle = "تعديل كلمة المرور";
            $('#UpdatePasswordDl').modal('show');
        }

        // Add missing admin navigation functions
        vm.AgencyPage = function () {
            $state.go('AgencyPage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.UsersPage = function () {
            $state.go('UsersPage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.BtnNews = function () {
            $state.go('NewsPage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.BtnLocalAgency = function () {
            $state.go('AgencyTraking_page', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.StockRequestsPage = function () {
            $state.go('StockRequestsPage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        vm.ContractTerminationPage = function () {
            $state.go('ContractTerminationPage', { 'UserID': vm.UserID, 'UserName': vm.UserName });
        }

        // إضافة دالة الانتقال لصفحة إدارة التصنيف الرئيسي
        vm.MainAccountPage = function () {
            $state.go('MainAccountPage', {
                UserID: $stateParams.UserID,
                UserName: $stateParams.UserName
            });
        };

        // SECURITY: Initialize session when coming from login (with URL parameters)
        // This ensures permissions are loaded after successful login
        function initializeHomePageSession() {
            // Always try to initialize if we have session data
            var sessionToken = localStorage.getItem('sessionToken');
            var userID = localStorage.getItem('userID');
            var userName = localStorage.getItem('userName');
            
            if (sessionToken && userID && userName) {
                vm.SessionToken = sessionToken;
                vm.UserID = userID;
                vm.UserName = userName;
                vm.CompID = localStorage.getItem('compID');
                blockUI.start();
                loadPermissionsDirectly();
            } else {
                vm.clearSessionData();
                $state.go('LoginPage');
            }
        }
        
        // Direct permission loading function
        function loadPermissionsDirectly() {
            if (!vm.SessionToken || !vm.UserID) {
                $state.go('LoginPage');
                return;
            }
            
            DataService.a26011(vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.News = Response.data.News;
                    vm.perm = Response.data.perm;
                    loadMainPermissions();
                } else {
                    blockUI.stop();
                    if (Response.data.ErrorCode == 3) {
                        vm.clearSessionData();
                        $state.go('LoginPage');
                    }
                }
            }, function (error) {
                blockUI.stop();
            });
        }
        
        // Load main permissions
        function loadMainPermissions() {
            DataService.a27016(vm.UserID).then(function (Response) {
                if (Response.data.ErrorCode == 0) {
                    vm.MainObj.AgCount = Response.data.SelCoAg;
                    vm.MainObj.CoCount = Response.data.SelCoCo;
                    vm.MainObj.UsCount = Response.data.SelCoUs;
                    vm.MainObj.ToCount = Response.data.SelCoTo;
                    vm.Perms = Response.data.Perms;
                    
                    if (!$scope.$$phase) {
                        $scope.$apply();
                    }
                    
                    setTimeout(function() {
                        if (window.reinitializeDropdowns) {
                            window.reinitializeDropdowns();
                        }
                        var event = new CustomEvent('permissionsLoaded');
                        document.dispatchEvent(event);
                    }, 300);
                    
                    blockUI.stop();
                } else {
                    blockUI.stop();
                    if (Response.data.ErrorCode == 3) {
                        vm.clearSessionData();
                        $state.go('LoginPage');
                    }
                }
            }, function (error) {
                blockUI.stop();
            });
        }

        // Initialize home page session on load
        initializeHomePageSession();
    }
})();