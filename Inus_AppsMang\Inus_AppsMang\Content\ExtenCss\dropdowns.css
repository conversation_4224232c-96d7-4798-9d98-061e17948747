/* Custom dropdown styles for AngularJS compatibility */
.dropdown-menu {
    display: none !important;
    position: absolute !important;
    z-index: 1050 !important;
    min-width: 160px;
    padding: 5px 0;
    margin: 0 !important;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.dropdown-menu.show {
    display: block !important;
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
    text-decoration: none;
    background-color: transparent;
    border: 0;
    cursor: pointer;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #262626;
    text-decoration: none;
    background-color: #f5f5f5;
}

/* Ensure dropdown positioning works correctly */
.dropdown {
    position: relative;
}

/* Fix for RTL direction */
.dropdown-menu[dir="ltr"] {
    text-align: left;
}

/* Enhanced dropdown styles */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    animation: dropdownFade 0.2s ease-in-out;
}

@keyframes dropdownFade {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dropdown item hover effects */
.dropdown-item {
    transition: all 0.2s ease;
    position: relative;
    padding-right: 30px;
}

.dropdown-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    padding-right: 35px;
}

/* RTL support for dropdowns */
[dir="rtl"] .dropdown-menu {
    text-align: right;
}

[dir="rtl"] .dropdown-item {
    padding-right: 20px;
    padding-left: 30px;
}

[dir="rtl"] .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
}

/* Dropdown divider */
.dropdown-divider {
    height: 1px;
    margin: 0.5rem 0;
    background-color: rgba(0, 0, 0, 0.1);
}

/* Dropdown header */
.dropdown-header {
    display: block;
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #6c757d;
    white-space: nowrap;
}

/* Dropdown with icons */
.dropdown-item i,
.dropdown-item .bi {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    font-size: 1.1em;
    vertical-align: middle;
}

/* Active dropdown item */
.dropdown-item.active,
.dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: var(--bs-primary);
}

/* Disabled dropdown item */
.dropdown-item.disabled,
.dropdown-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: transparent;
}

/* Dropdown menu dark theme */
.dropdown-menu-dark {
    background-color: rgba(33, 37, 41, 0.95) !important;
    border-color: rgba(255, 255, 255, 0.15);
}

.dropdown-menu-dark .dropdown-item {
    color: #dee2e6;
}

.dropdown-menu-dark .dropdown-item:hover,
.dropdown-menu-dark .dropdown-item:focus {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.15);
}

.dropdown-menu-dark .dropdown-divider {
    background-color: rgba(255, 255, 255, 0.15);
}

.dropdown-menu-dark .dropdown-header {
    color: #adb5bd;
}

/* Dropdown menu positioning */
.dropdown-menu-end {
    right: 0;
    left: auto;
}

.dropdown-menu-start {
    right: auto;
    left: 0;
}

/* Dropdown menu scrolling */
.dropdown-menu-scroll {
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
}

.dropdown-menu-scroll::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.dropdown-menu-scroll::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* Nested dropdowns */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: -1px;
}

.dropdown-submenu:hover > .dropdown-menu {
    display: block;
}

/* RTL support for nested dropdowns */
[dir="rtl"] .dropdown-submenu > .dropdown-menu {
    left: auto;
    right: 100%;
    margin-left: 0;
    margin-right: -1px;
} 