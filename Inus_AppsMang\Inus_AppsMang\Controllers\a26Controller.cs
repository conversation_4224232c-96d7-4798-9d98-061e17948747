﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using Inus_AppsMang;
using Inus_AppsMang.Models;

namespace InsuranceWebApp.Controllers
{
    public class a26Controller : Controller
    {
        Insh_AppsDBEntities db = new Insh_AppsDBEntities();
        public ActionResult a25001()
        {
            return PartialView("UsersPage");
        }

        public ActionResult a260071(perCalsss PerObj)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(PerObj.InsertBy.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var us_per = db.UserPermissions.Where(c => c.UserId == PerObj.selectedUserID).ToList();

                var per1 = us_per.Where(c => c.PermissionsList.EngName == "users").ToList();
                if (per1.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "users").ID;
                    obj.Status = (byte)(PerObj.users ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per1.Count() > 0)
                {
                    UserPermissions obj = per1[0];
                    obj.Status = (byte)(PerObj.users ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per2 = us_per.Where(c => c.PermissionsList.EngName == "agency").ToList();
                if (per2.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "agency").ID;
                    obj.Status = (byte)(PerObj.agency ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges(); 
                }
                if (per2.Count() > 0)
                {
                    UserPermissions obj = per2[0];
                    obj.Status = (byte)(PerObj.agency ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per3 = us_per.Where(c => c.PermissionsList.EngName == "comp_contries").ToList();
                if (per3.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_contries").ID;
                    obj.Status = (byte)(PerObj.comp_contries ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per3.Count() > 0)
                {
                    UserPermissions obj = per3[0];
                    obj.Status = (byte)(PerObj.comp_contries ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per4 = us_per.Where(c => c.PermissionsList.EngName == "comp_Color").ToList();
                if (per4.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_Color").ID;
                    obj.Status = (byte)(PerObj.comp_Color ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per4.Count() > 0)
                {
                    UserPermissions obj = per4[0];
                    obj.Status = (byte)(PerObj.comp_Color ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per5 = us_per.Where(c => c.PermissionsList.EngName == "settings").ToList();
                if (per5.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "settings").ID;
                    obj.Status = (byte)(PerObj.settings ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per5.Count() > 0)
                {
                    UserPermissions obj = per5[0];
                    obj.Status = (byte)(PerObj.settings ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per6 = us_per.Where(c => c.PermissionsList.EngName == "news").ToList();
                if (per6.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "news").ID;
                    obj.Status = (byte)(PerObj.news ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per6.Count() > 0)
                {
                    UserPermissions obj = per6[0];
                    obj.Status = (byte)(PerObj.news ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per7 = us_per.Where(c => c.PermissionsList.EngName == "palance").ToList();
                if (per7.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "palance").ID;
                    obj.Status = (byte)(PerObj.palance ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per7.Count() > 0)
                {
                    UserPermissions obj = per7[0];
                    obj.Status = (byte)(PerObj.palance ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per8 = us_per.Where(c => c.PermissionsList.EngName == "comp_seralies").ToList();
                if (per8.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_seralies").ID;
                    obj.Status = (byte)(PerObj.comp_seralies ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per8.Count() > 0)
                {
                    UserPermissions obj = per8[0];
                    obj.Status = (byte)(PerObj.comp_seralies ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per9 = us_per.Where(c => c.PermissionsList.EngName == "reports").ToList();
                if (per9.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "reports").ID;
                    obj.Status = (byte)(PerObj.reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per9.Count() > 0)
                {
                    UserPermissions obj = per9[0];
                    obj.Status = (byte)(PerObj.reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per10 = us_per.Where(c => c.PermissionsList.EngName == "comp_deleteAprovels").ToList();
                if (per10.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_deleteAprovels").ID;
                    obj.Status = (byte)(PerObj.comp_deleteAprovels ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per10.Count() > 0)
                {
                    UserPermissions obj = per10[0];
                    obj.Status = (byte)(PerObj.comp_deleteAprovels ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per11 = us_per.Where(c => c.PermissionsList.EngName == "comp_inshDurations").ToList();
                if (per11.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_inshDurations").ID;
                    obj.Status = (byte)(PerObj.comp_inshDurations ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per11.Count() > 0)
                {
                    UserPermissions obj = per11[0];
                    obj.Status = (byte)(PerObj.comp_inshDurations ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per12 = us_per.Where(c => c.PermissionsList.EngName == "comp_carTypes").ToList();
                if (per12.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_carTypes").ID;
                    obj.Status = (byte)(PerObj.comp_carTypes ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per12.Count() > 0)
                {
                    UserPermissions obj = per12[0];
                    obj.Status = (byte)(PerObj.comp_carTypes ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per13 = us_per.Where(c => c.PermissionsList.EngName == "comp_compaines").ToList();
                if (per13.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_compaines").ID;
                    obj.Status = (byte)(PerObj.comp_compaines ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per13.Count() > 0)
                {
                    UserPermissions obj = per13[0];
                    obj.Status = (byte)(PerObj.comp_compaines ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per14 = us_per.Where(c => c.PermissionsList.EngName == "traviles_Sys").ToList();
                if (per14.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "traviles_Sys").ID;
                    obj.Status = (byte)(PerObj.traviles_Sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per14.Count() > 0)
                {
                    UserPermissions obj = per14[0];
                    obj.Status = (byte)(PerObj.traviles_Sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per15 = us_per.Where(c => c.PermissionsList.EngName == "trav_contries").ToList();
                if (per15.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "trav_contries").ID;
                    obj.Status = (byte)(PerObj.trav_contries ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per15.Count() > 0)
                {
                    UserPermissions obj = per15[0];
                    obj.Status = (byte)(PerObj.trav_contries ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per16 = us_per.Where(c => c.PermissionsList.EngName == "trav_ranges").ToList();
                if (per16.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "trav_ranges").ID;
                    obj.Status = (byte)(PerObj.trav_ranges ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per16.Count() > 0)
                {
                    UserPermissions obj = per16[0];
                    obj.Status = (byte)(PerObj.trav_ranges ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per17 = us_per.Where(c => c.PermissionsList.EngName == "trav_Seariles").ToList();
                if (per17.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "trav_Seariles").ID;
                    obj.Status = (byte)(PerObj.trav_Seariles ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per17.Count() > 0)
                {
                    UserPermissions obj = per17[0];
                    obj.Status = (byte)(PerObj.trav_Seariles ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per18 = us_per.Where(c => c.PermissionsList.EngName == "trav_reports").ToList();
                if (per18.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "trav_reports").ID;
                    obj.Status = (byte)(PerObj.trav_reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per18.Count() > 0)
                {
                    UserPermissions obj = per18[0];
                    obj.Status = (byte)(PerObj.trav_reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per19 = us_per.Where(c => c.PermissionsList.EngName == "comp_Mangments").ToList();
                if (per19.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_Mangments").ID;
                    obj.Status = (byte)(PerObj.comp_Mangments ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per19.Count() > 0)
                {
                    UserPermissions obj = per19[0];
                    obj.Status = (byte)(PerObj.comp_Mangments ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per20 = us_per.Where(c => c.PermissionsList.EngName == "compolsy_Sys").ToList();
                if (per20.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "compolsy_Sys").ID;
                    obj.Status = (byte)(PerObj.compolsy_Sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per20.Count() > 0)
                {
                    UserPermissions obj = per20[0];
                    obj.Status = (byte)(PerObj.compolsy_Sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per21 = us_per.Where(c => c.PermissionsList.EngName == "orang_sys").ToList();
                if (per21.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "orang_sys").ID;
                    obj.Status = (byte)(PerObj.orang_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per21.Count() > 0)
                {
                    UserPermissions obj = per21[0];
                    obj.Status = (byte)(PerObj.orang_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per22 = us_per.Where(c => c.PermissionsList.EngName == "Orange_Contries").ToList();
                if (per22.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "Orange_Contries").ID;
                    obj.Status = (byte)(PerObj.Orange_Contries ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per22.Count() > 0)
                {
                    UserPermissions obj = per22[0];
                    obj.Status = (byte)(PerObj.Orange_Contries ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per23 = us_per.Where(c => c.PermissionsList.EngName == "Orang_cars").ToList();
                if (per23.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "Orang_cars").ID;
                    obj.Status = (byte)(PerObj.Orang_cars ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per23.Count() > 0)
                {
                    UserPermissions obj = per23[0];
                    obj.Status = (byte)(PerObj.Orang_cars ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per24 = us_per.Where(c => c.PermissionsList.EngName == "comp_reports").ToList();
                if (per24.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "comp_reports").ID;
                    obj.Status = (byte)(PerObj.comp_reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per24.Count() > 0)
                {
                    UserPermissions obj = per24[0];
                    obj.Status = (byte)(PerObj.comp_reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per25 = us_per.Where(c => c.PermissionsList.EngName == "orang_reports").ToList();
                if (per25.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "orang_reports").ID;
                    obj.Status = (byte)(PerObj.orang_reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per25.Count() > 0)
                {
                    UserPermissions obj = per25[0];
                    obj.Status = (byte)(PerObj.orang_reports ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per26 = us_per.Where(c => c.PermissionsList.EngName == "trav_deletedPapers").ToList();
                if (per26.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "trav_deletedPapers").ID;
                    obj.Status = (byte)(PerObj.trav_deletedPapers ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per26.Count() > 0)
                {
                    UserPermissions obj = per26[0];
                    obj.Status = (byte)(PerObj.trav_deletedPapers ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per27 = us_per.Where(c => c.PermissionsList.EngName == "medical_sys").ToList();
                if (per27.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "medical_sys").ID;
                    obj.Status = (byte)(PerObj.medical_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per27.Count() > 0)
                {
                    UserPermissions obj = per27[0];
                    obj.Status = (byte)(PerObj.medical_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per28 = us_per.Where(c => c.PermissionsList.EngName == "med_adress").ToList();
                if (per28.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "med_adress").ID;
                    obj.Status = (byte)(PerObj.med_adress ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per28.Count() > 0)
                {
                    UserPermissions obj = per28[0];
                    obj.Status = (byte)(PerObj.med_adress ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per29 = us_per.Where(c => c.PermissionsList.EngName == "med_nat").ToList();
                if (per29.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "med_nat").ID;
                    obj.Status = (byte)(PerObj.med_nat ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per29.Count() > 0)
                {
                    UserPermissions obj = per29[0];
                    obj.Status = (byte)(PerObj.med_nat ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per30 = us_per.Where(c => c.PermissionsList.EngName == "med_job").ToList();
                if (per30.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "med_job").ID;
                    obj.Status = (byte)(PerObj.med_job ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per30.Count() > 0)
                {
                    UserPermissions obj = per30[0];
                    obj.Status = (byte)(PerObj.med_job ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per31 = us_per.Where(c => c.PermissionsList.EngName == "marital_stat").ToList();
                if (per31.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "marital_stat").ID;
                    obj.Status = (byte)(PerObj.marital_stat ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per31.Count() > 0)
                {
                    UserPermissions obj = per31[0];
                    obj.Status = (byte)(PerObj.marital_stat ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per32 = us_per.Where(c => c.PermissionsList.EngName == "agency_sys").ToList();
                if (per32.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "agency_sys").ID;
                    obj.Status = (byte)(PerObj.agency_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per32.Count() > 0)
                {
                    UserPermissions obj = per32[0];
                    obj.Status = (byte)(PerObj.agency_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per33 = us_per.Where(c => c.PermissionsList.EngName == "finance_sys").ToList();
                if (per33.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "finance_sys").ID;
                    obj.Status = (byte)(PerObj.finance_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per33.Count() > 0)
                {
                    UserPermissions obj = per33[0];
                    obj.Status = (byte)(PerObj.finance_sys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per34 = us_per.Where(c => c.PermissionsList.EngName == "agency_deleteAprovels").ToList();
                if (per34.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "agency_deleteAprovels").ID;
                    obj.Status = (byte)(PerObj.agency_deleteAprovels ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per34.Count() > 0)
                {
                    UserPermissions obj = per34[0];
                    obj.Status = (byte)(PerObj.agency_deleteAprovels ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per35 = us_per.Where(c => c.PermissionsList.EngName == "InsuranceInterfacemanagmentSys").ToList();
                if (per35.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "InsuranceInterfacemanagmentSys").ID;
                    obj.Status = (byte)(PerObj.InsuranceInterfacemanagmentSys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per35.Count() > 0)
                {
                    UserPermissions obj = per35[0];
                    obj.Status = (byte)(PerObj.InsuranceInterfacemanagmentSys ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per36 = us_per.Where(c => c.PermissionsList.EngName == "Orang_Seariles").ToList();
                if (per36.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "Orang_Seariles").ID;
                    obj.Status = (byte)(PerObj.Orang_Seariles ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per36.Count() > 0)
                {
                    UserPermissions obj = per36[0];
                    obj.Status = (byte)(PerObj.Orang_Seariles ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per38 = us_per.Where(c => c.PermissionsList.EngName == "agincu_contractEnd").ToList();
                if (per38.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "agincu_contractEnd").ID;
                    obj.Status = (byte)(PerObj.agincu_contractEnd ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per38.Count() > 0)
                {
                    UserPermissions obj = per38[0];
                    obj.Status = (byte)(PerObj.agincu_contractEnd ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                var per39 = us_per.Where(c => c.PermissionsList.EngName == "inventory_a_requests").ToList();
                if (per39.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "inventory_a_requests").ID;
                    obj.Status = (byte)(PerObj.inventory_a_requests ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                if (per39.Count() > 0)
                {
                    UserPermissions obj = per39[0];
                    obj.Status = (byte)(PerObj.inventory_a_requests ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }

                // إضافة معالجة صلاحية المؤهل العلمي
                var per_med_academic = us_per.Where(c => c.PermissionsList.EngName == "med_academic").ToList();
                if (per_med_academic.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "med_academic").ID;
                    obj.Status = (byte)(PerObj.med_academic ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                else if (per_med_academic.Count() > 0)
                {
                    UserPermissions obj = per_med_academic[0];
                    obj.Status = (byte)(PerObj.med_academic ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }

                // إضافة معالجة صلاحية إدارة التصنيف الرئيسي
                var per_MainAcc = us_per.Where(c => c.PermissionsList.EngName == "MainAcc").ToList();
                if (per_MainAcc.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = db.PermissionsList.SingleOrDefault(c => c.EngName == "MainAcc").ID;
                    obj.Status = (byte)(PerObj.MainAcc ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                else if (per_MainAcc.Count() > 0)
                {
                    UserPermissions obj = per_MainAcc[0];
                    obj.Status = (byte)(PerObj.MainAcc ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }

                var per_med_percentage = us_per.Where(c => c.PermissionsList.EngName == "med_percentage").ToList();
                if (per_med_percentage.Count() == 0)
                {
                    UserPermissions obj = new UserPermissions();
                    obj.UserId = PerObj.selectedUserID;
                    obj.PermissionId = 111;
                    obj.Status = (byte)(PerObj.med_percentage ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Add(obj);
                    db.SaveChanges();
                }
                else if (per_med_percentage.Count() > 0)
                {
                    UserPermissions obj = per_med_percentage[0];
                    obj.Status = (byte)(PerObj.med_percentage ? 1 : 3);
                    obj.CreatedOn = DateTime.Now;
                    obj.CreatedBy = PerObj.InsertBy.ToString();
                    db.UserPermissions.Attach(obj);
                    db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                    db.SaveChanges();
                }
                return Json(new { ErrorCode = 0 });
            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجى الاتصال بفريق الدعم الفني " });
            }
        }
        //[ValidateAntiForgeryToken]
        public ActionResult a260070(Guid logedInID, Guid selectedUserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(logedInID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                perCalsss obj = new perCalsss();
                var Per = obj.getPerbyUserID(selectedUserID);

                return Json(new { ErrorCode = 0, Data = Per });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a250025(Guid logedInID, Guid selectedUserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(logedInID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                perCalsss obj = new perCalsss();
                var Per = obj.getPerbyUserID(selectedUserID);
                if (Per == null)
                {
                    return Json(new { ErrorCode = 2, Message = "لم يتم العثور على بيانات المستخدم" });
                }
                return Json(new { ErrorCode = 0, Data = Per });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a250013(string logedInID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(logedInID, 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var AllResAgencies = db.Agency.Where(p => p.Status == 1).
                      Select(c => new {

                          ID = c.AgencyID,
                          Name = c.AgencyName
                      }).ToList();
                return Json(new { ErrorCode = 0, AllResAgencies });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a260058(string[] AgencyIDs, Guid UserID, Guid InsertBy)
        {
            try
            {
                Insur_class ic = new Insur_class();

                if (!ic.HasPer(UserID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }

                foreach (var it in AgencyIDs)
                {
                    var agid = Guid.Parse(it);
                    var check = db.ResAgencies.Where(c => c.AgencyID == agid && c.UserID == UserID).ToList();
                    if (check.Count() == 0)
                    {
                        ResAgencies obj = new ResAgencies();
                        obj.ResAgenID = Guid.NewGuid();
                        obj.AgencyID = agid;
                        obj.ISdeleted = false;
                        obj.UserID = UserID;
                        obj.InsertDate = DateTime.Now;
                        obj.InsertBy = InsertBy;
                        db.ResAgencies.Add(obj);
                        db.SaveChanges();
                    }
                    else if (check.Count() == 1)
                    {
                        var item = check[0];
                        item.ISdeleted = false;
                        item.UpdateDate = DateTime.Now;
                        item.UpdateBy = InsertBy;
                        db.ResAgencies.Attach(item);
                        db.Entry(item).State = System.Data.Entity.EntityState.Modified;
                        db.SaveChanges();
                    }
                    else
                        return Json(new { ErrorCode = 5, });
                }
                return Json(new { ErrorCode = 0, });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }


        public ActionResult a250057(Guid SelectedUserID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var ResAgenLI = db.Agency.Where(c => c.Status == 1 && c.ResAgencies
                .Where(a => a.ISdeleted == false && a.UserID == SelectedUserID).Count() == 0).Select(p => new
                {
                    ID = p.AgencyID,
                    Name = p.AgencyName,
                }).ToList();

                return Json(new { ErrorCode = 0, ResAgenLI });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }

        public ActionResult a260056(Guid SelectedUserID, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var obj = db.ResAgencies.Where(c => c.UserID == SelectedUserID && c.ISdeleted == false).Select(p => new
                {
                    ID = p.ResAgenID,
                    AgenId = p.AgencyID,
                    AgenName = p.Agency.AgencyName,
                    p.InsertBy,
                    p.InsertDate
                }).OrderByDescending(p => p.InsertDate).ToList();
                var ResAgenciesTb = obj.Select(p => new {
                    p.ID,
                    p.AgenId,
                    p.AgenName,
                    InsertDate = p.InsertDate.Value.ToString("yyyy-MM-dd"),
                    InsertBy = db.Users.Find(p.InsertBy).FullName
                }).ToList();
                return Json(new { ErrorCode = 0, ResAgenciesTb });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }



        public ActionResult a250055(Guid UserID, Guid SelectedUserID, List<ID_Name> SelectedAg, List<ID_Name> UnSelectedAg)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                var RestPer = db.ResAgencies.Where(c => c.UserID == SelectedUserID).ToList();
                List<Guid> RsIds = RestPer.Where(p => p.ISdeleted == false).Select(p => (Guid)p.AgencyID).ToList();
                List<Guid> NotRsIds = RestPer.Where(p => p.ISdeleted == true).Select(p => (Guid)p.AgencyID).ToList();
                var SelAg = SelectedAg?.Where(c => c != null).Where(p => !RsIds.Contains(p.ID)).ToList();
                var NotSelAg = UnSelectedAg?.Where(c => c != null).Where(p => !NotRsIds.Contains(p.ID)).ToList();
                //var SelAg = SelectedAg.Count() > 0 ? RsIds != null ? SelectedAg.Where(c => ! RsIds?.Contains(c.ID)).ToList(): SelectedAg : null;
                //var NotSelAg = UnSelectedAg.Count() > 0 ? NotRsIds != null ?  UnSelectedAg.Where(c => ! NotRsIds.Contains(c.ID)).ToList():
                //    UnSelectedAg : null;
                if (SelAg != null)
                    foreach (var it in SelAg)
                    {
                        if (RestPer.Where(c => c.AgencyID == it.ID).Any())
                        {

                            ResAgencies rs = RestPer.SingleOrDefault(c => c.AgencyID == it.ID);
                            rs.ISdeleted = false;
                            db.Entry(rs).State = System.Data.Entity.EntityState.Modified;
                            db.SaveChanges();
                        }
                        else
                        {
                            ResAgencies rs = new ResAgencies();
                            rs.ResAgenID = Guid.NewGuid();
                            rs.UserID = UserID;
                            rs.AgencyID = it.ID;
                            rs.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                            rs.InsertBy = UserID;
                            rs.ISdeleted = false;
                            db.ResAgencies.Add(rs);
                            db.SaveChanges();
                        }
                    }
                if (NotSelAg != null)
                    foreach (var it in NotSelAg)
                    {

                        if (RestPer.Where(c => c.AgencyID == it.ID).Any())
                        {

                            ResAgencies rs = RestPer.SingleOrDefault(c => c.AgencyID == it.ID);
                            rs.ISdeleted = true;
                            db.Entry(rs).State = System.Data.Entity.EntityState.Modified;
                            db.SaveChanges();
                        }
                        else
                        {
                            ResAgencies rs = new ResAgencies();
                            rs.ResAgenID = Guid.NewGuid();
                            rs.UserID = UserID;
                            rs.AgencyID = it.ID;
                            rs.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                            rs.InsertBy = UserID;
                            rs.ISdeleted = true;
                            db.ResAgencies.Add(rs);
                            db.SaveChanges();
                        }
                    }
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a25007(Guid SelectedUserID, byte Status, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 });
                }
                Users obj = db.Users.Find(SelectedUserID);
                obj.Status = Status;
                obj.UpdateDate = DateTime.Now.ToUniversalTime().AddHours(2);
                obj.UpdatedBy = UserID.ToString();
                db.Users.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0, });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a25006(Guid SelectedUserID, byte Status, Guid UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID.ToString(), 10))
                {
                    return Json(new { ErrorCode = 3 });
                }
                Users obj = db.Users.Find(SelectedUserID);
                obj.Status = Status;
                obj.UpdateDate = DateTime.Now.ToUniversalTime().AddHours(2);
                obj.UpdatedBy = UserID.ToString();
                db.Users.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
                return Json(new { ErrorCode = 0, });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult IsExisit(string Parm1, Guid? Param2)
        {
            try
            {
                if (Parm1 == null)
                    return Json(new { ErrorCode = 0, IsEx = false });
                int x = Parm1.ToList().Count();
                if (Parm1.ToList().Count() > 20 || Parm1.Contains("Select"))
                {
                    return Json(new { ErrorCode = 5, ErorrMessage = "لقد تجاوزت الطول المسموح لإسم المستخدم" });
                }
                bool IsEx;
                IsEx = db.Users.Where(c => c.UserName == Parm1 && c.UserID != Param2).ToList().Count() == 0 ? false : true;
                return Json(new { ErrorCode = 0, IsEx });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a25005(UserClass mainObj, string[] Perms)
        {
            try

            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(mainObj.InsertBy, 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                Guid usid = Guid.Parse(mainObj.UsID);

                var us = db.Users.Where(p => p.UserName == mainObj.UsName && p.Status != 3 && p.UserID != usid).ToList();
                if (us.Count() > 0)
                    return Json(new { ErrorCode = 5, ErorrMessage = "اسم المستخدم مدرج مسبقا" });
                Users obj = db.Users.Find(usid);

                obj.UserName = mainObj.UsName;
                obj.FullName = mainObj.FullName;
                obj.Passowrd = mainObj.Password;
                obj.Status = 1;
                obj.UpdatedBy = mainObj.InsertBy;
                obj.PhoneNum1 = mainObj.PhoneNum;
                obj.UserTypeID = mainObj.UsType;
                obj.IsRptUser = mainObj.UsType > 1;
                obj.UpdateDate = DateTime.Now.ToUniversalTime().AddHours(2);
                db.Users.Attach(obj);
                db.Entry(obj).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
        
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception ex)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a25002(string UserID, string logedInID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(logedInID, 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                Guid gus = Guid.Parse(UserID);
                var ReAgencies = db.Agency.Where(c => c.Status != 3).Select(p =>
                  new
                  {
                      Name = p.AgencyName,
                      ID = p.AgencyID,
                      ResCount = db.ResAgencies.Where(c => c.ISdeleted == false && c.AgencyID == p.AgencyID && c.UserID == gus).Count() >= 1 ? true : false,
                      ResCount1 = db.ResAgencies.Where(c => c.ISdeleted == false && c.AgencyID == p.AgencyID && c.UserID == gus).Count()

                  }).ToList();
                var ResAgHasPer = ReAgencies.Where(c => c.ResCount == true).ToList();
                var ResAgNoPer = ReAgencies.Where(c => c.ResCount == false).ToList();
                return Json(new { ErrorCode = 0, ResAgHasPer, ResAgNoPer });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a25003(UserClass mainObj, string[] Perms)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(mainObj.InsertBy, 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                Users obj = new Users();
                obj.UserID = Guid.NewGuid();
                obj.UserName = mainObj.UsName;
                obj.FullName = mainObj.FullName;
                obj.Passowrd = mainObj.Password;
                obj.UserTypeID = mainObj.UsType;
                obj.IsRptUser = mainObj.UsType > 1;
                obj.Status = 1;
                obj.InsertedBy = mainObj.InsertBy;
                obj.PhoneNum1 = mainObj.PhoneNum;
                obj.UserTypeID = mainObj.UsType;
                obj.InsertDate = DateTime.Now.ToUniversalTime().AddHours(2);
                db.Users.Add(obj);
                db.SaveChanges();
              
                return Json(new { ErrorCode = 0 });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
        public ActionResult a25004(bool IsAll, string UserID)
        {
            try
            {
                Insur_class ic = new Insur_class();
                if (!ic.HasPer(UserID, 10))
                {
                    return Json(new { ErrorCode = 3 }); ;
                }
                List<byte> st = IsAll == true ? new List<byte> { 1, 2 } : new List<byte> { 1 };

                var ss = db.Users.Where(c => st.Contains((byte)c.Status)).Select(p => new
                {
                    p.UserID,
                    p.UserName,
                    p.UserTypeID,
                    p.Passowrd,
                    p.FullName,
                    p.PhoneNum1,
                    p.IsRptUser,
                    p.Status,
                    p.InsertDate,
                    p.InsertedBy,
                    PerCount = p.ResAgencies.Where(c => c.ISdeleted == false).Count()

                }).OrderByDescending(c => c.InsertDate).ToList();
                var UserObj = ss.Select(p => new
                {
                    UsID = p.UserID,
                    UsName = p.UserName,
                    UsType = p.UserTypeID,
                    p.FullName,
                    PhoneNum = p.PhoneNum1,
                    //UsType =( p.IsRptUser  ?? false)? 2 : 1,
                    p.Passowrd,
                    p.Status,
                    InsertDate = p.InsertDate.Value.ToString("yyyy-MM-dd hh:mm:ss tt"),
                    InsertedBy = p.InsertedBy == null ? "-" : db.Users.Find(Guid.Parse(p.InsertedBy)).FullName,
                    p.PerCount
                }).ToList();
                return Json(new { ErrorCode = 0, UserObj });

            }
            catch (Exception)
            {
                return Json(new { ErrorCode = 1, ErrorMessage = "يرجاء الاتصال بفريق الدعم الفني " });
            }
        }
    }
}