﻿@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="modal fade cairo" id="Dl_Message" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sfsdf">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {{ctrl.DlMessage}}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">رجوع</button>
                <button type="button" class="btn btn-primary" ng-click="ctrl.Stop()">نعم</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade cairo" id="Dl_MessageComp" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sfsdf">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-danger">
                {{ctrl.DlMessageUs}}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">رجوع</button>
                <button type="button" class="btn btn-primary" ng-click="ctrl.a400030()">نعم</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade cairo" id="Info" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" >
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sfsdf">معلومات الإدخال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h4 class="col-12 text-center fs-3 m-4 text-dark">أضيف من قبل : {{ctrl.InsertBy}}</h4>
                        <h4 class="col-12 text-center fs-3 m-4 text-dark">تاريخ الإضافة : {{ctrl.InsertedDate}}</h4>

                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary col-12" data-bs-dismiss="modal">موافق</button>

            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center" ng-if="ctrl.ShowFlag == 0">
    <div class="col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12 ">

        <div class="card w-100">

            <div class="card  navglassColor ">
                <div class="card-header gradientModelBackg">

                    <div class="btn-group cairo" dir="ltr" role="group" aria-label="Basic mixed styles example">
                        <button type="button" disabled class="btn btn-primary"> واجهة إدارة الوكلاء</button>
                        @*<button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>*@
                        <button type="button" ng-click="ctrl.sys_page()" class="btn btn-success bi bi-house-fill"> <span class="m-2">أنظمة الإدارة</span></button>
                    </div>
                </div>


            </div>

            <div class="card-body w-100">
                <div class="row">
                    <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 col-xl-5 col-xxl-5">
                        <div class="form-group ">

                            <input class="inputStyleWhite cairo" type="text" ng-model="search.All" placeholder="بحث ...">

                        </div>

                        <div class="form-group  mt-2 cairo">

                            <select name="CatID" required
                                    ng-options="x.ID as x.Name for x in ctrl.AgenCatsFilter "
                                    class="form-select"
                                    ng-model="search.CatID">
                                <option class="select2-search" value="">جميع التصنيفات </option>
                                <option value="">جميع التصنيفات </option>
                            </select>

                        </div>

                        <div class="form-group  mt-2 cairo">

                            <select name="MainAccID" 
                                    ng-options="x.ID as x.Name for x in ctrl.MainAccList"
                                    class="form-select"
                                    ng-model="search.MainAccID">
                                <option value="">جميع التصنيفات الرئيسية</option>
                            </select>

                        </div>
                    </div>
                    <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12 col-xl-5 col-xxl-5">



                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 col-xl-2 col-xxl-2 mt-1">

                        <button type="button"
                                class="btn btn-secondary  mt-4 cairo"
                                data-bs-toggle="modal" data-bs-target="#NewItem" ng-click="ctrl.AddNew()">
                            إضافة مركز
                            <i class="fa fa-building"></i>
                        </button>

                    </div>
                </div>
                <div class="row m-2 cairo">
                    <div class="table-responsive">
                        <table class=" table  table-striped table-hover">
                            <tr>
                                <th class="text-center">#</th>
                                <th class="text-center">نوع المركز</th>
                                <th class="text-center">اسم المركز</th>
                                <th class="text-center"> إضافة بواسطة</th>
                                <th class="text-center">  تاريخ الاضافة</th>
                                <th class="text-center">رقم المركز </th>
                                <th class="text-center">الدولة </th>
                                <th class="text-center">المدينة </th>
                                <th class="text-center">التصنيف الرئيسي</th>

                                <th class="text-center">الحالة</th>
                                <th class="text-center" colspan="4">العمليات</th>
                            </tr>
                            <tr ng-repeat="x in ctrl.AgencyList|filter:search.All |filter:{AgenCatID:search.CatID} |filter:{MainAccID:search.MainAccID}">

                                <td class="text-center " ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{$index + 1}}</td>
                                <td class="text-center " ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.AgencyType}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.AgencyName}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.InsertedBy}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.InsertedDate}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.Agnum}}</td>

                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.CountryName}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.CityName}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.MainAccName}}</td>

                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.Status == 1 ? 'مفعل':'موقوف'}}</td>

                                <td>

                                    <div class="dropdown">
                                        <button class="btn btn-danger dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            العمليات
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><button class="dropdown-item" ng-click="ctrl.a400059(x)">الصلاحيات</button></li>

                                            <li><button class="dropdown-item" ng-click="ctrl.a400020(x)">الشركات</button></li>
                                            <li><button class="dropdown-item" ng-click="ctrl.a400080(x)">العهد</button></li>
                                            <li>
                                                <button class="dropdown-item" ng-click="ctrl.BtnMainEdit(x)">
                                                    <i class="fa fa-edit"></i>تعديل
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" ng-show="x.Status == 1" ng-click="ctrl.BtnStop(x,2)">
                                                    <i class="fa fa-toggle-off"></i>إيقاف
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" ng-show="x.Status == 2" ng-click="ctrl.BtnStop(x,1)">
                                                    <i class="fa fa-toggle-on"></i>تفعيل
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" ng-click="ctrl.BtnStop(x,3)">
                                                    <i class="fa fa-trash"></i>حذف
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row cairo justify-content-center" ng-if="ctrl.ShowFlag == 1">
    <div class="col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12 ">

        <div class="card">

            <div class="card  navglassColor ">
                <div class="card-header gradientModelBackg">

                    <div class="btn-group cairo" dir="ltr" role="group" aria-label="Basic mixed styles example">
                        <button type="button" disabled class="btn btn-primary"> الشركات </button>
                        <button type="button" class="btn btn-primary" ng-click="ctrl.ShowFlag=0">واجهة إدارة العملاء </button>
                        <button type="button" ng-click="ctrl.BtnHomePage()" class="btn btn-success bi bi-house-fill"> <span class="m-2">الرئيسية</span></button>
                    </div>
                </div>


            </div>

            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 col-xl-6 col-xxl-6">
                        <div class="form-group ">

                            <input class="inputStyleWhite cairo" type="text" ng-model="search.Name" placeholder="بحث بإسم الشركة ...">

                        </div>

                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 col-xl-6 col-xxl-6">

                        <button type="button"
                                class="btn btn-secondary btn-lg float-end col-lg-4 col-md-4 col-xl-4 col-xxl-4 col-sm-12 col-xs-12 cairo"
                                data-bs-toggle="modal" ng-click="ctrl.a400022()">
                            إضافة شركة
                            <i class="fa fa-building"></i>
                        </button>

                    </div>
                </div>
                <div class="row m-2 cairo" style="height:90vh; padding:15px;overflow-y:scroll">
                    <div class="table-responsive">
                        <table class=" table  table-striped table-hover">
                            <tr>
                                <th class="text-center">#</th>
                                <th class="text-center"> اسم الشركة</th>
                                <th class="text-center"> تمت الاضافة </th>
                                <th class="text-center">   تاريخ الاضافة    </th>
                                <th class="text-center"> الحالة  </th>
                                <th class="text-center" colspan="4">العمليات</th>
                            </tr>
                            <tr ng-repeat="x in ctrl.companies|filter:{Name:search.Name}">
                                <td class="text-center " ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{$index + 1}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.compName}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.InsertBy}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.InsertDate}}</td>
                                <td class="text-center" ng-click="ctrl.Info(x)" style=" cursor: pointer;">{{x.Status == 1 ? 'مفعل':'موقوف'}}</td>
                                <td ng-show="x.Status == 1">
                                    <button type="button" class="btn btn-warning col-12" ng-click="ctrl.a400029(x,2)">
                                        إيقاف
                                        <i class="fa fa-toggle-off"></i>
                                    </button>
                                </td>
                                <td ng-show="x.Status == 2">
                                    <button type="button" class="btn btn-success col-12" ng-click="ctrl.a400029(x,1)">
                                        تفعيل
                                        <i class="fa fa-toggle-on"></i>
                                    </button>

                                <td ng-show="x.Status == 0">
                                    <button type="button" class="btn btn-success col-12" ng-click="ctrl.a400029(x,1)">
                                        تفعيل
                                        <i class="fa fa-toggle-on"></i>
                                    </button>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger col-12" ng-click="ctrl.a400029(x,3)">
                                        حذف
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" ng-if="ctrl.ShowFlag == 3">

    <div class="btn-group mx-2  " dir="ltr" role="group" aria-label="Basic example">
        <button type="button" disabled class="btn btn-dark  ">
            <i class="fa fa-users" aria-hidden="true"></i>
            صلاحيات المستخدمين
        </button>
        <button type="button" class="btn btn-success  " ng-click="ctrl.ShowFlag = 0">
            <i class="fa fa-users" aria-hidden="true"></i>
            إدارة المستخدمين
        </button>
        <button type="button" class="btn btn-primary bi bi-house-door" ng-click="ctrl.sys_page()"> الصفحة الرئيسية</button>
    </div>

    <h1></h1>
    <h3 class="text-center" style="font-family:'Times New Roman'">صلاحيات المستخدم:  {{ctrl.selectedusername}}</h3>

    <div class="card border-1 border-secondary col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary pt-4 text-center" style="font-family: 'Times New Roman'; color: gray !important; width: 900px !important">
                الصلاحيات العامة
            </h4>

            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.AgPerObj.Cump_Sys" id="ch_compolsy_Sys" name="compolsy_Sys" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_compolsy_Sys">نظام الإجباري</label>
                </div>

                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.AgPerObj.Travel_Sys" id="ch_Travel_Sys" name="Travel_Sys" autocomplete="off">
                    <label class="btn btn-outline-success" for="ch_Travel_Sys">نظام المسافرين</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.AgPerObj.Orange_Sys" id="ch_Orange_Sys" name="Orange_Sys" autocomplete="off">
                    <label class="btn btn-outline-warning" for="ch_Orange_Sys">نظام البرتقالية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.AgPerObj.Med_ResponsibilitySys" id="ch_Med_ResponsibilitySys" name="Med_ResponsibilitySys" autocomplete="off">
                    <label class="btn btn-outline-danger" for="ch_Med_ResponsibilitySys">نظام المسؤولية الطبية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-model="ctrl.AgPerObj.inventory_requests" id="ch_inventory_requests" name="inventory_requests" autocomplete="off">
                    <label class="btn btn-outline-secondary" for="ch_inventory_requests">طلبات المخزون</label>
                </div>
            </div>
        </div>
    </div>


    <div class="card border-1 border-primary col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.AgPerObj.Cump_Sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-primary text-center" style="font-family: 'Times New Roman'; color: blue !important; width: 900px !important">
                صلاحيات نظام الإجباري
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@
            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Cump_Sys" ng-model="ctrl.AgPerObj.Cump_ChangeOwnership" id="ch_Cump_ChangeOwnership" name="Cump_ChangeOwnership" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_Cump_ChangeOwnership">تغيير الملكية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Cump_Sys" ng-model="ctrl.AgPerObj.Cump_AdditionalServices" id="ch_Cump_AdditionalServices" name="Cump_AdditionalServices" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_Cump_AdditionalServices">الخدمات الإضافية</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Cump_Sys" ng-model="ctrl.AgPerObj.Cump_OldInterface" id="ch_Cump_OldInterface" name="Cump_OldInterface" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_Cump_OldInterface">واجهة المنظومة القديمة</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Cump_Sys" ng-model="ctrl.AgPerObj.print_normal" id="ch_print_normal" name="print_normal" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_print_normal">طباعة بدون هوامش</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Cump_Sys" ng-model="ctrl.AgPerObj.paper_A4" id="ch_paper_A4" name="paper_A4" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_paper_A4"> A4 طباعة ورقة </label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Cump_Sys" ng-model="ctrl.AgPerObj.colored_paper" id="ch_colored_paper" name="colored_paper" autocomplete="off">
                    <label class="btn btn-outline-primary" for="ch_colored_paper">طباعة ورقة بالهوامش</label>
                </div>
            </div>
        </div>
    </div>

    <div class="card border-1 border-success col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.AgPerObj.Travel_Sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center" style="font-family: 'Times New Roman'; color: green !important; width: 900px !important">
                صلاحيات نظام المسافرين
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@
            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Travel_Sys" ng-model="ctrl.AgPerObj.Trav_Calculation" id="ch_Trav_Calculation" name="Trav_Calculation" autocomplete="off">
                    <label class="btn  btn-outline-success" for="ch_Trav_Calculation">إحتساب وثيقة</label>
                </div>
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Travel_Sys" ng-model="ctrl.AgPerObj.Trav_ShowAllDocs" id="ch_Trav_ShowAllDocs" name="Trav_ShowAllDocs" autocomplete="off">
                    <label class="btn  btn-outline-success" for="ch_Trav_ShowAllDocs">عرض مركز الإصدار</label>
                </div>
            </div>

        </div>
    </div>

    <div class="card border-1 border-warning col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.AgPerObj.Orange_Sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-secondary text-center justify-content-center" style="font-family: 'Times New Roman'; color: orange !important; height: 50px !important; width:900px !important ">
                صلاحيات نظام البرتقالية
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@
            <div class="float-start">

            </div>

        </div>
    </div>
    <div class="card border-1 border-danger col-lg-12 col-md-12 col-md-12 col-sm-12 col-xs-12 col-xl-12 col-xxl-12" style="height:200px !important" ng-if="ctrl.AgPerObj.Orange_Sys">
        <div class="row pt-4 justify-content-center">
            <h4 class="card-header text-danger text-center justify-content-center" style="font-family: 'Times New Roman'; color: red !important; height: 50px !important; width:900px !important ">
                صلاحيات نظام المسؤولية الطبية
            </h4>
            @*<h1 class=" text-secondary">____________________________________________________________________________________</h1>*@
            <div class="row pt-2 justify-content-center">
                <div class="col-auto">
                    <input type="checkbox" class="btn-check" ng-if="ctrl.AgPerObj.Med_ResponsibilitySys" ng-model="ctrl.AgPerObj.Un_countedper" id="ch_Un_countedper" name="Un_countedper" autocomplete="off">
                    <label class="btn  btn-outline-danger" for="ch_Un_countedper">نسبة غير محددة</label>
                </div>
            </div>

        </div>
    </div>
    <div class="modal-footer justify-content-center pt-2">
        <button type="button" class="btn cairo btn-success col-lg-3 col-md-3 col-sm-3 col-xl-3 col-xxl-3 justify-content-center" ng-click="ctrl.a400071()"
               >
            حفظ
        </button>
    </div>
</div>


<div class="modal fade cairo" id="NewUser" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

                <form name="FrmUs" class="row g-2" autocomplete="off" novalidate>
                    <div class="row">
                        <div class="col-12 ">
                            <label for="companies"> بحث باسم الشركة </label>
                            <input required maxlength="20" minlength="3" name="companies" type="text"
                                   class="form-control ">

                        </div>

                        <div class="col-12 mt-2 ">
                            <div class="form-group ">
                                <label for="companies">   قائمة الشركات  </label>
                                <select ng-options="  x.ID as x.Name for x in  ctrl.ListCO " ng-model="ctrl.Comp_ID" class="form-control" size="4">
                                    <option> </option>
                                </select>
                            </div>

                        </div>
                    </div>
                </form>


            </div>
            <div class="modal-footer" ng-show="ctrl.DlFalg == 0">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal" ng-click="ctrl.a4000025()"
                        ng-disabled="FrmUs.$invalid || ctrl.IsExitUs">
                    اضافة
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>


        </div>
    </div>
</div>

<div class="modal fade cairo" id="NewItem" tabindex="-1" data-bs-backdrop="static" aria-labelledby="{{ctrl.Title}}" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{ctrl.DlTitle}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

                <form name="Frm" class="needs-validation" autocomplete="off" novalidate>
                    <!-- Header Section with Agency Info and Image -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-building"></i> معلومات المركز الأساسية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- Agency Image -->
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="position-relative">
                                            <img height="200" width="200" id="ImgPerview"
                                                 class="img-fluid rounded border shadow-sm"
                                                 ng-src="{{ctrl.obj.image}}"
                                                 style="cursor: pointer; object-fit: cover;" />
                                            <div class="position-absolute bottom-0 end-0">
                                                <label for="ImgPath" class="btn btn-sm btn-primary rounded-circle">
                                                    <i class="bi bi-camera"></i>
                                                </label>
                                            </div>
                                        </div>
                                        <input name="ImgPath" id="ImgPath" class="d-none"
                                               type="file" fileinput="ctrl.ImageFile"
                                               filepreview="ctrl.obj.image" accept="image/*" />
                                        <small class="text-muted d-block mt-2">شعار المركز</small>
                                    </div>
                                </div>

                                <!-- Agency Details -->
                                <div class="col-md-9">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label for="AgencyType" class="form-label fw-semibold">
                                                <i class="bi bi-tag"></i> نوع المركز
                                            </label>
                                            <select name="AgencyType" required
                                                    class="form-select" ng-class="{'is-invalid': Frm.AgencyType.$touched && Frm.AgencyType.$invalid, 'is-valid': Frm.AgencyType.$touched && Frm.AgencyType.$valid}" 
                                                    ng-model="ctrl.obj.AgencyType"
                                                    ng-change="ctrl.AgnTypeChanged()" ng-disabled="ctrl.DlFalg == 1">
                                                <option value="">اختر نوع المركز</option>
                                                <option value="1">فرع للشركة</option>
                                                <option value="2">شركات أخرى</option>
                                            </select>
                                            <div class="invalid-feedback" ng-show="Frm.AgencyType.$touched && Frm.AgencyType.$error.required">
                                                يجب إدخال هذا الحقل
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <label for="CatID" class="form-label fw-semibold">
                                                <i class="bi bi-grid"></i> تصنيف المركز
                                            </label>
                                            <select name="CatID" required ng-change="ctrl.li_Selected()"
                                                    ng-options="x.ID as x.Name for x in ctrl.AgenCats"
                                                    class="form-select" ng-class="{'is-invalid': Frm.CatID.$touched && Frm.CatID.$invalid, 'is-valid': Frm.CatID.$touched && Frm.CatID.$valid}" 
                                                    ng-model="ctrl.obj.CatID">
                                                <option value="">الرجاء الإختيار</option>
                                            </select>
                                            <div class="invalid-feedback" ng-show="Frm.CatID.$touched && Frm.CatID.$error.required">
                                                يجب إدخال هذا الحقل
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <label for="MainAccID" class="form-label fw-semibold">
                                                <i class="bi bi-layer-group"></i> التصنيف الرئيسي
                                            </label>
                                            <select name="MainAccID" required
                                                    ng-options="x.ID as x.Name for x in ctrl.MainAccList"
                                                    class="form-select" ng-class="{'is-invalid': Frm.MainAccID.$touched && Frm.MainAccID.$invalid, 'is-valid': Frm.MainAccID.$touched && Frm.MainAccID.$valid}" 
                                                    ng-model="ctrl.obj.AgenMainAccID">
                                                <option value="">اختر التصنيف الرئيسي</option>
                                            </select>
                                            <div class="invalid-feedback" ng-show="Frm.MainAccID.$touched && Frm.MainAccID.$error.required">
                                                يجب إدخال هذا الحقل
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <label for="ContryCoID" class="form-label fw-semibold">
                                                <i class="bi bi-hash"></i> رقم المركز
                                            </label>
                                            <input ng-model="ctrl.obj.ContryCoID" type="number"
                                                   min="1" name="ContryCoID" 
                                                   class="form-control" ng-class="{'is-invalid': Frm.ContryCoID.$touched && Frm.ContryCoID.$invalid, 'is-valid': Frm.ContryCoID.$touched && Frm.ContryCoID.$valid}" 
                                                   required>
                                            <div class="invalid-feedback" ng-show="Frm.ContryCoID.$touched && Frm.ContryCoID.$error.required">
                                                الرجاء إدخال هذا الحقل
                                            </div>
                                            <div class="invalid-feedback" ng-show="Frm.ContryCoID.$touched && Frm.ContryCoID.$error.min">
                                                اقل قيمة يمكن إدخالها 1
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="Name" class="form-label fw-semibold">
                                                <i class="bi bi-type"></i> إسم المركز
                                            </label>
                                            <input required maxlength="80" minlength="3" name="Name" type="text"
                                                   ng-model="ctrl.obj.Name" 
                                                   class="form-control" ng-class="{'is-invalid': Frm.Name.$touched && Frm.Name.$invalid, 'is-valid': Frm.Name.$touched && Frm.Name.$valid}">
                                            <div class="invalid-feedback" ng-show="Frm.Name.$touched && Frm.Name.$error.required">
                                                يجب إدخال هذا الحقل
                                            </div>
                                            <div class="invalid-feedback" ng-show="Frm.Name.$touched && Frm.Name.$error.minlength">
                                                اقل عدد حروف 3
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="NameEn" class="form-label fw-semibold">
                                                <i class="bi bi-type"></i> Agency Name (English)
                                            </label>
                                            <input required maxlength="80" minlength="3" name="NameEn" type="text"
                                                   ng-model="ctrl.obj.NameEn" 
                                                   class="form-control" ng-class="{'is-invalid': Frm.NameEn.$touched && Frm.NameEn.$invalid, 'is-valid': Frm.NameEn.$touched && Frm.NameEn.$valid}">
                                            <div class="invalid-feedback" ng-show="Frm.NameEn.$touched && Frm.NameEn.$error.required">
                                                يجب إدخال هذا الحقل
                                            </div>
                                            <div class="invalid-feedback" ng-show="Frm.NameEn.$touched && Frm.NameEn.$error.minlength">
                                                اقل عدد حروف 3
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location Information -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="bi bi-geo-alt"></i> معلومات الموقع</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="FID" class="form-label fw-semibold">
                                        <i class="bi bi-flag"></i> الدولة
                                    </label>
                                    <select name="FID" required ng-disabled="ctrl.IsliDisFlag"
                                            class="form-select" ng-class="{'is-invalid': Frm.FID.$touched && Frm.FID.$invalid, 'is-valid': Frm.FID.$touched && Frm.FID.$valid}" 
                                            ng-change="ctrl.a40004()"
                                            ng-model="ctrl.obj.FID">
                                        <option value="">الرجاء الإختيار</option>
                                        <option ng-repeat="x in ctrl.CountryFilter | orderBy : 'CountryName'" value="{{x.CountryID}}">
                                            {{x.CountryName}}
                                        </option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.FID.$touched && Frm.FID.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <label for="SID" class="form-label fw-semibold">
                                        <i class="bi bi-building"></i> المدينة
                                    </label>
                                    <select name="SID" required
                                            ng-options="x.CityID as x.CityName for x in ctrl.CityFilter | orderBy : 'CityName'"
                                            class="form-select" ng-class="{'is-invalid': Frm.SID.$touched && Frm.SID.$invalid, 'is-valid': Frm.SID.$touched && Frm.SID.$valid}" 
                                            ng-model="ctrl.obj.SID">
                                        <option value="">الرجاء الإختيار</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.SID.$touched && Frm.SID.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <label for="PhoneNum" class="form-label fw-semibold">
                                        <i class="bi bi-telephone"></i> رقم الهاتف
                                    </label>
                                    <input pattern="^[0-9\-\+\s\(\)]+$"
                                           ng-model="ctrl.obj.PhoneNum" name="PhoneNum" 
                                           class="form-control" ng-class="{'is-invalid': Frm.PhoneNum.$touched && Frm.PhoneNum.$invalid, 'is-valid': Frm.PhoneNum.$touched && Frm.PhoneNum.$valid}" 
                                           required>
                                    <div class="invalid-feedback" ng-show="Frm.PhoneNum.$touched && Frm.PhoneNum.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.PhoneNum.$touched && Frm.PhoneNum.$error.pattern">
                                        الرجاء إدخال رقم الهاتف بشكل صحيح
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Information -->
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="bi bi-currency-dollar"></i> المعلومات المالية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="IsLocalCurrnecy" class="form-label fw-semibold">
                                        <i class="bi bi-cash"></i> العملة
                                    </label>
                                    <select name="IsLocalCurrnecy" required ng-disabled="ctrl.IsliDisFlag"
                                            class="form-select" ng-class="{'is-invalid': Frm.IsLocalCurrnecy.$touched && Frm.IsLocalCurrnecy.$invalid, 'is-valid': Frm.IsLocalCurrnecy.$touched && Frm.IsLocalCurrnecy.$valid}" 
                                            ng-model="ctrl.obj.IsLocalCurrnecy">
                                        <option value="">اختر العملة</option>
                                        <option value="0">دينار ليبي</option>
                                        <option value="1">دولار</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.IsLocalCurrnecy.$touched && Frm.IsLocalCurrnecy.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <label for="IntiaiPalance" class="form-label fw-semibold">
                                        <i class="bi bi-wallet"></i> الرصيد الإبتدائي
                                    </label>
                                    <input ng-model="ctrl.obj.IntiaiPalance" type="number"
                                           min="0" step="0.01" name="IntiaiPalance" 
                                           class="form-control" ng-class="{'is-invalid': Frm.IntiaiPalance.$touched && Frm.IntiaiPalance.$invalid, 'is-valid': Frm.IntiaiPalance.$touched && Frm.IntiaiPalance.$valid}" 
                                           required>
                                    <div class="invalid-feedback" ng-show="Frm.IntiaiPalance.$touched && Frm.IntiaiPalance.$error.required">
                                        الرجاء إدخال الرصيد الإبتدائي
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.IntiaiPalance.$touched && Frm.IntiaiPalance.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                    <small class="text-muted">الرصيد الإبتدائي للمركز عند الإضافة</small>
                                </div>

                                <div class="col-md-4">
                                    <label for="LemitDayesPaym" class="form-label fw-semibold">
                                        <i class="bi bi-calendar-check"></i> حد أيام الدفع الأخير
                                    </label>
                                    <input ng-model="ctrl.obj.LemitDayesPaym" type="number"
                                           min="1" name="LemitDayesPaym" 
                                           class="form-control" ng-class="{'is-invalid': Frm.LemitDayesPaym.$touched && Frm.LemitDayesPaym.$invalid, 'is-valid': Frm.LemitDayesPaym.$touched && Frm.LemitDayesPaym.$valid}" 
                                           required>
                                    <div class="invalid-feedback" ng-show="Frm.LemitDayesPaym.$touched && Frm.LemitDayesPaym.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.LemitDayesPaym.$touched && Frm.LemitDayesPaym.$error.min">
                                        اقل قيمة يمكن إدخالها 1
                                    </div>
                                    <small class="text-muted">عدد الأيام المسموح بها للدفع الأخير</small>
                                </div>

                                <div class="col-md-4">
                                    <label for="UnusedDocsLimt" class="form-label fw-semibold">
                                        <i class="bi bi-file-earmark-x"></i> حد الوثائق غير المستخدمة
                                    </label>
                                    <input ng-model="ctrl.obj.UnusedDocsLimt" type="number"
                                           min="0" name="UnusedDocsLimt" 
                                           class="form-control" ng-class="{'is-invalid': Frm.UnusedDocsLimt.$touched && Frm.UnusedDocsLimt.$invalid, 'is-valid': Frm.UnusedDocsLimt.$touched && Frm.UnusedDocsLimt.$valid}" 
                                           required>
                                    <div class="invalid-feedback" ng-show="Frm.UnusedDocsLimt.$touched && Frm.UnusedDocsLimt.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.UnusedDocsLimt.$touched && Frm.UnusedDocsLimt.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                    <small class="text-muted">الحد الأقصى للوثائق غير المستخدمة</small>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="bi bi-credit-card"></i> إعدادات الآجل</h5>
                        </div>
                        <div class="card-body">
                  
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2"><i class="bi bi-gear"></i> الإجباري </h6>
                                </div>
                                <div class="col-md-6">
                                    <label for="IsPercentge" class="form-label fw-semibold"> نوع هامش الربح</label>
                                    <select name="IsPercentge" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.IsPercentge.$touched && Frm.IsPercentge.$invalid, 'is-valid': Frm.IsPercentge.$touched && Frm.IsPercentge.$valid}" 
                                            ng-model="ctrl.AgenProft[0].IsPercentge">
                                        <option value="">اختر النوع</option>
                                        <option value="0">قيمة</option>
                                        <option value="1">نسبة</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.IsPercentge.$touched && Frm.IsPercentge.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6" >
                                    <label for="Value" class="form-label fw-semibold">قيمة /نسبة  هامش الربح</label>
                                    <input required ng-model="ctrl.AgenProft[0].Value" type="number" min="0" name="Value"
                                           class="form-control" ng-class="{'is-invalid': Frm.Value.$touched && Frm.Value.$invalid, 'is-valid': Frm.Value.$touched && Frm.Value.$valid}" >
                                    <div class="invalid-feedback" ng-show="Frm.Value.$touched && Frm.Value.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Value.$touched && Frm.Value.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="IsADibt" class="form-label fw-semibold">السماح بالآجل</label>
                                    <select name="IsADibt" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.IsADibt.$touched && Frm.IsADibt.$invalid, 'is-valid': Frm.IsADibt.$touched && Frm.IsADibt.$valid}" 
                                            ng-model="ctrl.obj.IsADibt">
                                        <option value="">اختر الخيار</option>
                                        <option value="0">لا</option>
                                        <option value="1">نعم</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.IsADibt.$touched && Frm.IsADibt.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6" ng-show="ctrl.obj.IsADibt == 1">
                                    <label for="debtVal" class="form-label fw-semibold">قيمة الدين المسموح</label>
                                    <input ng-model="ctrl.obj.debtVal" type="number" min="0" name="debtVal"
                                           class="form-control" ng-class="{'is-invalid': Frm.debtVal.$touched && Frm.debtVal.$invalid, 'is-valid': Frm.debtVal.$touched && Frm.debtVal.$valid}" 
                                           ng-required="ctrl.obj.IsADibt == '1'">
                                    <div class="invalid-feedback" ng-show="Frm.debtVal.$touched && Frm.debtVal.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.debtVal.$touched && Frm.debtVal.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h6 class="text-warning border-bottom pb-2"><i class="bi bi-airplane"></i>  البرتقالية</h6>
                                </div>
                                <div class="col-md-6">
                                    <label for="IsPercentge1" class="form-label fw-semibold"> نوع هامش الربح</label>
                                    <select name="IsPercentge1" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.IsPercentge1.$touched && Frm.IsPercentge1.$invalid, 'is-valid': Frm.IsPercentge1.$touched && Frm.IsPercentge1.$valid}" 
                                            ng-model="ctrl.AgenProft[1].IsPercentge">
                                        <option value="">اختر النوع</option>
                                        <option value="0">قيمة</option>
                                        <option value="1">نسبة</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.IsPercentge1.$touched && Frm.IsPercentge1.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Value1" class="form-label fw-semibold">قيمة /نسبة  هامش الربح</label>
                                    <input required ng-model="ctrl.AgenProft[1].Value" type="number" min="0" name="Value1"
                                           class="form-control" ng-class="{'is-invalid': Frm.Value1.$touched && Frm.Value1.$invalid, 'is-valid': Frm.Value1.$touched && Frm.Value1.$valid}">
                                    <div class="invalid-feedback" ng-show="Frm.Value1.$touched && Frm.Value1.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Value1.$touched && Frm.Value1.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Trav_IsADibt" class="form-label fw-semibold">السماح بالآجل</label>
                                    <select name="Trav_IsADibt" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.Trav_IsADibt.$touched && Frm.Trav_IsADibt.$invalid, 'is-valid': Frm.Trav_IsADibt.$touched && Frm.Trav_IsADibt.$valid}" 
                                            ng-model="ctrl.obj.Orang_IsADibt">
                                        <option value="">اختر الخيار</option>
                                        <option value="0">لا</option>
                                        <option value="1">نعم</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.Trav_IsADibt.$touched && Frm.Trav_IsADibt.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6" ng-show="ctrl.obj.Orang_IsADibt == 1">
                                    <label for="Trav_debtVal" class="form-label fw-semibold">قيمة الدين المسموح</label>
                                    <input ng-model="ctrl.obj.Orang_AllowedDibtValue" type="number" min="0" name="Trav_debtVal"
                                           class="form-control" ng-class="{'is-invalid': Frm.Trav_debtVal.$touched && Frm.Trav_debtVal.$invalid, 'is-valid': Frm.Trav_debtVal.$touched && Frm.Trav_debtVal.$valid}" 
                                           ng-required="ctrl.obj.Orang_IsADibt == '1'">
                                    <div class="invalid-feedback" ng-show="Frm.Trav_debtVal.$touched && Frm.Trav_debtVal.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Trav_debtVal.$touched && Frm.Trav_debtVal.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <h6 class="text-success border-bottom pb-2"><i class="bi bi-airplane"></i>  المسافرين</h6>
                                </div>
                                <div class="col-md-6">
                                    <label for="IsPercentge2" class="form-label fw-semibold"> نوع هامش الربح</label>
                                    <select name="IsPercentge2" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.IsPercentge2.$touched && Frm.IsPercentge2.$invalid, 'is-valid': Frm.IsPercentge2.$touched && Frm.IsPercentge2.$valid}" 
                                            ng-model="ctrl.AgenProft[2].IsPercentge">
                                        <option value="">اختر النوع</option>
                                        <option value="0">قيمة</option>
                                        <option value="1">نسبة</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.IsPercentge2.$touched && Frm.IsPercentge2.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Value2" class="form-label fw-semibold">قيمة /نسبة  هامش الربح</label>
                                    <input required ng-model="ctrl.AgenProft[2].Value" type="number" min="0" name="Value2"
                                           class="form-control" ng-class="{'is-invalid': Frm.Value2.$touched && Frm.Value2.$invalid, 'is-valid': Frm.Value2.$touched && Frm.Value2.$valid}">
                                    <div class="invalid-feedback" ng-show="Frm.Value2.$touched && Frm.Value2.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Value2.$touched && Frm.Value2.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Trav_IsADibt2" class="form-label fw-semibold">السماح بالآجل</label>
                                    <select name="Trav_IsADibt2" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.Trav_IsADibt2.$touched && Frm.Trav_IsADibt2.$invalid, 'is-valid': Frm.Trav_IsADibt2.$touched && Frm.Trav_IsADibt2.$valid}" 
                                            ng-model="ctrl.obj.Trav_IsADibt">
                                        <option value="">اختر الخيار</option>
                                        <option value="0">لا</option>
                                        <option value="1">نعم</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.Trav_IsADibt2.$touched && Frm.Trav_IsADibt2.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6" ng-show="ctrl.obj.Trav_IsADibt == 1">
                                    <label for="Trav_debtVal2" class="form-label fw-semibold">قيمة الدين المسموح</label>
                                    <input ng-model="ctrl.obj.Trav_debtVal" type="number" min="0" name="Trav_debtVal2"
                                           class="form-control" ng-class="{'is-invalid': Frm.Trav_debtVal2.$touched && Frm.Trav_debtVal2.$invalid, 'is-valid': Frm.Trav_debtVal2.$touched && Frm.Trav_debtVal2.$valid}" 
                                           ng-required="ctrl.obj.Trav_IsADibt == '1'">
                                    <div class="invalid-feedback" ng-show="Frm.Trav_debtVal2.$touched && Frm.Trav_debtVal2.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Trav_debtVal2.$touched && Frm.Trav_debtVal2.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                            </div>


                            <div class="row g-3">
                                <div class="col-12">
                                    <h6 class="text-danger border-bottom pb-2"><i class="bi bi-heart-pulse"></i> التأمين المسرولية الطبية</h6>
                                </div>
                                <div class="col-md-6">
                                    <label for="IsPercentge3" class="form-label fw-semibold"> نوع هامش الربح</label>
                                    <select name="IsPercentge3" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.IsPercentge3.$touched && Frm.IsPercentge3.$invalid, 'is-valid': Frm.IsPercentge3.$touched && Frm.IsPercentge3.$valid}" 
                                            ng-model="ctrl.AgenProft[3].IsPercentge">
                                        <option value="">اختر النوع</option>
                                        <option value="0">قيمة</option>
                                        <option value="1">نسبة</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.IsPercentge3.$touched && Frm.IsPercentge3.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Value3" class="form-label fw-semibold">قيمة /نسبة  هامش الربح</label>
                                    <input required ng-model="ctrl.AgenProft[3].Value" type="number" min="0" name="Value3"
                                           class="form-control" ng-class="{'is-invalid': Frm.Value3.$touched && Frm.Value3.$invalid, 'is-valid': Frm.Value3.$touched && Frm.Value3.$valid}">
                                    <div class="invalid-feedback" ng-show="Frm.Value3.$touched && Frm.Value3.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Value3.$touched && Frm.Value3.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Med_IsADibt" class="form-label fw-semibold">السماح بالآجل</label>
                                    <select name="Med_IsADibt" required 
                                            class="form-select" ng-class="{'is-invalid': Frm.Med_IsADibt.$touched && Frm.Med_IsADibt.$invalid, 'is-valid': Frm.Med_IsADibt.$touched && Frm.Med_IsADibt.$valid}" 
                                            ng-model="ctrl.obj.MidRes_IsADibt">
                                        <option value="">اختر الخيار</option>
                                        <option value="0">لا</option>
                                        <option value="1">نعم</option>
                                    </select>
                                    <div class="invalid-feedback" ng-show="Frm.Med_IsADibt.$touched && Frm.Med_IsADibt.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                </div>
                                <div class="col-md-6" ng-show="ctrl.obj.MidRes_IsADibt == 1">
                                    <label for="Med_debtVal" class="form-label fw-semibold">قيمة الدين المسموح</label>
                                    <input ng-model="ctrl.obj.MidRes_AllowedDibtValue" type="number" min="0" name="Med_debtVal"
                                           class="form-control" ng-class="{'is-invalid': Frm.Med_debtVal.$touched && Frm.Med_debtVal.$invalid, 'is-valid': Frm.Med_debtVal.$touched && Frm.Med_debtVal.$valid}" 
                                           ng-required="ctrl.obj.MidRes_IsADibt == '1'">
                                    <div class="invalid-feedback" ng-show="Frm.Med_debtVal.$touched && Frm.Med_debtVal.$error.required">
                                        الرجاء إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Med_debtVal.$touched && Frm.Med_debtVal.$error.min">
                                        اقل قيمة يمكن إدخالها 0
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="bi bi-person-circle"></i> بيانات المستخدم</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="AgFullName" class="form-label fw-semibold">
                                        <i class="bi bi-person"></i> الاسم الثلاثي
                                    </label>
                                    <input required maxlength="50" minlength="3" name="AgFullName" type="text"
                                           ng-model="ctrl.obj.AgFullName" 
                                           class="form-control" ng-class="{'is-invalid': Frm.AgFullName.$touched && Frm.AgFullName.$invalid, 'is-valid': Frm.AgFullName.$touched && Frm.AgFullName.$valid}">
                                    <div class="invalid-feedback" ng-show="Frm.AgFullName.$touched && Frm.AgFullName.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.AgFullName.$touched && Frm.AgFullName.$error.minlength">
                                        اقل عدد حروف 3
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label for="Aname" class="form-label fw-semibold">
                                        <i class="bi bi-at"></i> إسم المستخدم
                                    </label>
                                    <input required maxlength="20" minlength="3" name="Aname" type="text"
                                           ng-model="ctrl.obj.Aname" ng-click="ctrl.ReUserName()"
                                           class="form-control" ng-class="{'is-invalid': (Frm.Aname.$touched && Frm.Aname.$invalid) || ctrl.ErrorCode == 5, 'is-valid': Frm.Aname.$touched && Frm.Aname.$valid && ctrl.ErrorCode != 5}" 
                                           pattern="^(?=.*[a-z])(?!.*\s).*$">
                                    <div class="invalid-feedback" ng-show="Frm.Aname.$touched && Frm.Aname.$error.required">
                                        يجب إدخال إسم المستخدم
                                    </div>
                                    <div class="invalid-feedback" ng-show="ctrl.ErrorCode == 5">
                                        {{ctrl.InsErrorMess}}
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Aname.$touched && Frm.Aname.$error.pattern">
                                        أدخل الحقل بشكل صحيح
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Aname.$touched && Frm.Aname.$error.minlength">
                                        اقل عدد حروف 3
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label for="Apass" class="form-label fw-semibold">
                                        <i class="bi bi-lock"></i> كلمة المرور
                                    </label>
                                    <input required maxlength="20" pattern="^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.*\s).*$"
                                           minlength="8" name="Apass" type="password"
                                           ng-model="ctrl.obj.Apass" 
                                           class="form-control" ng-class="{'is-invalid': Frm.Apass.$touched && Frm.Apass.$invalid, 'is-valid': Frm.Apass.$touched && Frm.Apass.$valid}">
                                    <div class="invalid-feedback" ng-show="Frm.Apass.$touched && Frm.Apass.$error.required">
                                        يجب إدخال هذا الحقل
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Apass.$touched && Frm.Apass.$error.pattern">
                                        كلمة المرور يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام
                                    </div>
                                    <div class="invalid-feedback" ng-show="Frm.Apass.$touched && Frm.Apass.$error.minlength">
                                        اقل عدد حروف 8
                                    </div>
                                    <small class="text-muted">كلمة المرور يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>


            </div>
            <div class="modal-footer cairo" ng-show="ctrl.DlFalg == 0">
                <button type="button" class="btn btn-success col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.Save()"
                        ng-disabled="Frm.$invalid">
                    حفظ
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>
            <div class="modal-footer" ng-show="ctrl.DlFalg == 1">
                <button type="button" class="btn btn-primary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" ng-click="ctrl.Edit()"
                        ng-disabled="Frm.$invalid" data-bs-dismiss="modal">
                    تعديل
                </button>
                <button type="button" class="btn btn-secondary col-lg-4 col-md-4 col-sm-4 col-xl-4 col-xxl-4" data-bs-dismiss="modal">إلغاء الأمر</button>
            </div>

        </div>
    </div>
</div>